{"GlobalPropertiesHash": "lOjQz4K+mFSU9U6KcM1mTGBlbDoqxgfIohCp+yUB+54=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["ddDfcoAECv+GUOGsgEXOy8vQZFt3QeST/PHn1flxsKE=", "K2nQLchwdXSSv+X0lB/XiAKyHR4jGSWi+rvC8tq0X4k=", "s8SjezCtAodYvnOW1VuIklu5qTbq/GHxrksCISHaylM=", "7wNEWTTP9j1cYz7SFKDJ1NOhhEFSsvklV46ontQ/fEA=", "rDWXzB1jprVi/g+vtUe3Lsz9omYtcp/q2edK4tW2UoA=", "o8N6pjlw9AtPFjE3PBEnjrHrKUN5KFj7mCWfm6AXuR0=", "M9LBXJVOzB4u5E7EcgwFZLf8txssYuOGTb9vZTXzP/o=", "6aENFBrWoQXUGE+DuAQiIT4RjWufT//JMsatjW+sBdw=", "YEzIeOvcgR0ZQWvtveeV9GIpVFTOzwebi4XZABt7fsY=", "0X3UFyiz9u9XZ20hBjJ4xYo/HrCq3ooqp1/BSeIVGJE=", "Q5WYa5ADEeyH/8WYMM9D86W0lY2Wp21prNIVkmSBIYY=", "PDNDxRbJoU2KJpEh2+kzNsYS/NSx0dbwMuYzvpR8B2w=", "02orfFK1ZHBETTPHdPknkFDUH0zibFRf4WhZ/zjVdIs=", "gt8tT3t23LN3wvVMY96kKvnkRJKSa2PTT9Ms+p4oJbM=", "tQurZPS+pOllw2bucHLF9GgPn9jwbU9I6en7UgSadDo=", "hjShpbAkV6VfqLEoZhKV6DapbyUXWtCedQYbMDVVzUE=", "6sH7xnS0zP3g6x0+nlRQ0Thehr2p8nLgoW4DNGxLtKY=", "tsLCZ5znWELi6KZBuUjtzZfazMrSOtwDPPc5tdAQFMI=", "XrAgVcyL/KgRI0cHF9DXbLVcigZjFOgNgugyplpbG2A=", "7vUiMO5juOa+Wn03kqc4X+QdnhV5710Hno7uAjehClk=", "Cl7qjaOd6BHbiSZW/gQNnx7UsHY2LtnKM5zxyYCZJEM=", "ka9DuaK+5gZyxYN0b+yL24oNeDMXjL0muV/+hayGV/8=", "Y0H3RuzdY5Q/z2UfKkiclPAzNTXCvOd1lkpOiJwlRhc=", "MrzPulDvFRePcMAAFzF0l2X609D7k/8mppXu07gn39o=", "Xgd0Zaiji1ndYb2H075D0fa+vxIwRJ+wGEyRS2D+cuk=", "fxNJXt1I/a2lphBc2vW0p7QasAzboNp+E0gIfc6QbJ8=", "aCwMuy/NBTIOqe/AjDI4AsNeDsBUG8MyrWZu1Z6rQBY=", "ddclm15Vl04xsP0QZJ//hR5x2ZcjaUAeDc2iPeXh84s=", "x2E4QJRiktcYK+lQgW6AawNJcky8pTXkHYU77fSYu/4=", "wRpo54BMU7wMy3NDFaPb36Wi2qtwQohS170GDkjuvMQ=", "uIa2FvcuYPAK1ps/jBI/gaojpBvMVxNjX145RAR/6yw=", "UdRbestliBJqw2wfhW3iA6yo6qPBy1PtAVb0pInPON8=", "JZOVpWOQ1yqMST1yE5bCKp4IEj4Ej5LGG0C0Zh/KaCU=", "2hQgKC86bAYpft8RWeXS9sqWhpmccyQxl+2vSJVWkIA=", "MevGEOJVXC/DH6pKjZrILNjwMQ8KlfnBQ5uYrJNU+Y0=", "4/Dz8wyZDBJCqdzPzISg6b7OGLXpwd36qAq5Y3ND4VE=", "6cgiNws9lpzhqnSgjegeJ9VwYGkyptiaVkY1658fgZo=", "7drLw4rJch30WAw+pw3X3g7/f54RVHMGDdfWEJ/GvEA=", "stxwkZAvOL50XuTwMipGHl6hijL14Au+5HntWu1SYzE=", "CM/HNp7fxTMcF8sGLeeKhwVulMuujlus7Kps3eGsDhk=", "poLopRO0zPTAzU/AIemfmJXVjnwL9xpvLJHa3CzMExI=", "11GMYeTuO7Gy29OraVbJPByax7Q+z6EY+q62LNr1WSw=", "fNApgZFAD/ZcRgU2r29A+EeI8quYeuZEI76SQ30HdqU=", "i3iv0lbt+/hnP18yqVZBVMq9/7g+blylLUv3+Cop0vo=", "/BZcIuZ1MxVSYsDygzZGT4Vjc0TZVfkjW3ccYyVXp5s=", "ocAYIuo9mPLVSDriM8UofanUlxFuHoypY1206JQ8TAw=", "LoPu2saB/JZByQMXlxAmFBovNd+T76MkbdKHeky1+dw=", "IahLfBA1w3lqF8WoHFjQ6CoXgvtzctrxuRZ0scb5/xc=", "vz1PWPp07pxhtu4vqwQ90u4ACQ87FkYohn4iePcJvjQ=", "BKmfvmwERUU1OXxvMvVX7phhr5bGxw6YK22kv/Cfm/k=", "PXj6IIZQbZCX2i37vcYNdaiJ3ZyKzoeOVUKSqRFCNa4=", "PbjmvJv0oHLGElX3IOu/aRwdt7jabdapmkuJUa0fkN8=", "t37KCCJaJBcIdGzqVHd473NOApbDWeLb0b3z0FcSzlk=", "CDCitGyaYw+JZfdRXoVs5J3JoPzbU5Z7vTGK0UJOunM=", "GCRqxmWFGXzTIpgosIHnEgHprS40sSQA4qnJGXTv7BQ=", "AKI7mIEjH22qpt14IlIpweHYbseiYmG07RM7GBzbDs8=", "eSjGJkAeQt7AYvdU7mtk0cWf3LZEKUJ7raYSy+eU5lk=", "DMiQUIRMy32HMGlnfjb+vXHOvL5q93J/uBq+6OIpkSo=", "zn9Jlp+/ivpnJnKPtllC+Rgwp2GYgjzTqTkTgP8+I+Y=", "8hZsSdGd9UUt/Zh4DDo4iCs5W7VSaj2D6YMDvtzRUJ8=", "7wrF5lHMCIDz5m6etPba0xTXam0cA1uWBiO0ex3yNoA=", "kOm1f+pg1878y7bbIEcCz2K05N1vGiDxvjEnCO8HEgg=", "2YhLd8rPiqxLPJMtT56o9wQJY7YRDC7LSLyoVkiYi6g=", "MLJ75ZtG9W4DADzg9LLQjALKaMr2HBGKnSHjaYrmox0=", "HLSywy370E0uJ4Qg+Um5jYWslhQQFEVRNCcmsL7hRCQ=", "Hc8gAKbCjRhjRw8jKLB9LdAi1qqJ4XHdh2PasfMmD/s=", "caAkhR/orBM6N6e/ZS43w4fQHGtPMnDMiwa8iVG7glk=", "XvVOF5FGZdh1PN48PVjvZo0LV5em+vz9HkRCCCcZyIA=", "5FX002MPhrLy/XIe0cc1fe84issfP7VaQK0hXXu1BK0=", "sKslpchj8it3oS4LCYpTXW9v2p/F3XYqFNNKWa82OVE=", "3s+qiHbF0Y//Z9yMdu4hnSaEXFxH1c3GRkvRGtGrqzQ=", "A+G3Mmlk0Nk/HiN1/qzwODRxohwcHJ+nPAwgwVAx46g=", "97t+GBnMUQbZepRBW5psqY5E0di6aJamUFfBCVG3/lc=", "TMF6NfaOiRMJJt+ALfCEpf3AHqPBaS4IGVGHoBNX0xI=", "dhvNuvru5hzgUwL2A2PCwmav63D7q9eCNFCt3gP0JH8=", "mp4BZXaTux4lj7vvWplXiGwoCr85qVNasy7QDL5V3/w=", "6pnTURRqSPEzLMiFQ7HIDMbkQKaFGUF4Qzip1eCIdZ4=", "5UKlQIeLMklMW82n06CpEnc1jUTvQf1CLC6vlw2YsyU=", "fOnoiIGGqOCDIaSomiuChVwmkGreROjOQIdsvXeIQcY=", "EjkuAe1zhQaammDeiAxXasY0ZFlS4Epb+r+4QllmJu0=", "K/BfoA6YXwvLkh/Jck6NLAQ/akD7E6Gm76vGkrcMJOM=", "vo3yQqNuJq5mCsZp4CDiR2Am+yctZdXWgS4XK5SRDMY=", "AiYWHnRvIxD8YnaaGv8+TwH/kwBjuGkAXvBITXCjJxA=", "WtSgu8AKSma7j7iegeZgktq7b+aRFhRhSFYn7QOIi8k=", "kQcI3IPByjxROusSRONt8HdkoPHLyWp0W7gQefvnbEQ=", "LuVmhUiefqB0Zf2W7uXtrv5vtiTCfv3t4roLsuKUMLk=", "oZ2t248iTyLAfsOS5NdADGgGHZeMwTIh0qIha2TnyWo=", "B/KlJQdvvH1v7S+ocbNBRFgBzQHlToX1csfrEDkCU08=", "F1GYbxinqUDPkMf1Hc6qgd/omyCdGijTHnt/HV9r07M=", "RS54BwTPRPXzgFS0w5S3dRSGQzHURVYUykd5KpzTR2A=", "/0wBBv08SenfbzWTZByxjY38EAm53eusFACdraqdylk=", "du1+gjzK8HNMeUQgtYqqTbf3NSLPp9RLx9mgSiZpG/4=", "3uq9VkrP16A4iTXCCROPuyeCl53SWUVHHIIYd7F128s=", "ZVwITI56VGEESXTKdiC6VsolZbS2Ckp9D6JY6rrrwX0=", "FxBnp6n5/t1J5JRaGHgv8rWXW8wzscc3DiKjah8qCmU=", "3vx5ph+DLM9d17PLXocSDZWPv0pxRSFWQyXEmkZqxCY=", "p7pK83kyFaZxq9lDJQqMHNyW3n3b8ZGgAHWNmHtqfZ0=", "6o77aLb2Yi5PpLxlP0Wn+u7JkBdUpyJf4Fb8B+ylalg=", "C8vMfFk7Z6/kz0WrUiBtYBrU+JNi4p8WgNspkVVs3JI=", "FGPDp4BB/ppEI++h+2Z012estiIZIniG16y+7Peebnk=", "Zvs2BAHZOx1fftUThFAr2awu+H6aDSskA61lUTkbses=", "Ke8A+pWEvnNLvPiacatMYDgE/gb9/q4lig6uSPNT3E4=", "nIXoHcykqE8c0NIs7mXRM6yfhJfiXks0a1CUjjgg73Y=", "1RoT08k0FeCvibBTxk+UdWqCwbKiFRE60CKm+d+Gs/4=", "3kHB+841qOm+3chD6pfZVVz3MZT4OvSwVUeJ/U9jNks=", "xC1PD444i/s/tkstIgnyF8Ixt0H99yxTw1wi022aR6g=", "ereWBWei3J1uUGrogp/P+WT0XTey4/g1YY8kkTvLzZs=", "VHoLU+fj4oFYNEWkMnXrc/RZzI+zNDAQRjSzKXGdnnY=", "IFXCW5X+uL3oXJztsyS2XahHmsovUtFxdEwxcR+ZkZk=", "wYVtd7OtV19vIJbrahTQlNSYj+6WHDIDy3Sw3LtRhqE=", "jeZko1lKabKIva/TK3DrfAmDvNH4wvu4d9YjSUQgIbs=", "h8hWjljxBNuo5LWuOJP9gvkx312zfBIqa52irzzN4rU=", "elFWxDVifVWJ2401dF1e8W+RO/qm5NM97IeiUclU2cE=", "S/WISznWoIT54XIaYguqRbbTvRU6Yj5r4q9/8wtAXR4=", "oSjjRzxKoLE8AKVvVhASfbcIAr0lmhSn7LwOaxS9UHk=", "JLYgc/neZxmb/8bie+9b+zpVeONh8CtWzTPYG5i1HnE=", "cQSp+gRAEfCQU6wa5w07fltaAZM7cP83IaAs3C81eI4=", "RgQ9IZYmypwcgGodtoS4A/zut1ICf5h2nsVo/A+pj08=", "dfKEycahMfy5gyD3jkJSxr+7Ac6QalfRWbPuTCpO93s=", "JM32C2a0IjH9n9I2ujAjjdmvCA5iA8qtoT/DjSDux6M=", "gTYuBL9u+EYhMqVYg6GVpXbIAjan3XXj0wsFuF9abwE=", "2gxG8a+mGxHfYMgS1i3kiNCndzWcmT6i+NNbOnaQMU8=", "q/KDmhdxlqqKP9JywpnSZ+FH5/EyiSiowDjE0ry/IDY=", "4uyVLimWfZeRX6SwtmXoy0vevxgaBq1a+xtLuZ1MxDk=", "VoZSG//lSaHOBHBXqzIbyVmanU8XWuqiBj04v6fMQ/I=", "WnEV/l5cneBCK0nsvQVEqncH39RPvN4SAJjrAmaQToE=", "9W6EcjCIlxadtjw14SSFdSSWFExy/rlG0Q2IyLOsfJ0=", "DZEbfrJFm52fwEQjOJXjCkE72HPragKUJiLc2itdyuw=", "+whGHgfTldPe3soOT/RWjgbIHxdhSCqmAH6NZqCOEPc=", "jp0dPP2MqUkiPzjwxIpSb1+7vqMLxrvpLqE0qTmDZU4=", "rF7EnaTL0Etda3k81WGQSvZVLuObKIeByfDQGR6dWCY=", "DvxySGpbS3OG9tkab7WYRRL0o2xcg99ZA1Y85CyToVQ=", "+/lNBda8hmtJMgaxJnI5w8XB+SN0sNaLjwmPn2QpOc8=", "orjtjZxjP/uo9tbrd5Ob9vyxgznDa+u8MsfnC+YM4Cw=", "dfG6at8+hC6E4y2s4lygGJ6TAWRTJplu4b0ss9z9+H8=", "NbaB1hrtz4FysH9Uaa69AZOcWlhkgixkCe2IAqI4VvI=", "Unn1FG0edxcLjipzNhwpojBIEiOmRUdJOdfZmHSXAro=", "6L1YMw5IOPNDDCXRds3z8xG0lpj0GDHOUAgmVgbfrBg=", "AYfMU+LYkZzpED52TIPgywa4pqYbTlB9/gdYfB5Ax0w=", "K3SeqrKQF8qhbjqy42NtgkC5OQoJ9rJ9mRpqHh1IY0U=", "2Q1XIo1omvH2y2NFtQ1Ks29H2WbWtewBZ9zV5nclrFc=", "55gTlAVQaJ6iXiV5jyBac9nQzzh/ZKH412JjCz6MHV8=", "M79NN+6ypie1/Blg3WeFoTvo/fCzZg/D+a+7wBrCW10=", "my0tupT3+2hz9YX78fR4cSutZE2jiIY7VqYXLcZo9ao=", "IrVE25sJamlDmuF32SBk+/EaQEnZnYke7lZB+PPnrkw=", "VgFjm4G85aOgHzzOLjB3Kl8baONSRtpRjgznsAeFMn4=", "iU8zMkyN8ovKh27DLK93Xo3ng0sBhK8AFyC6ZaQFUVU=", "Bm+xxMvfc9W8QOppxdnVPcgTgCvKXpJv353Hd0dAnIY=", "tWd3QVmhUodmnzzBiIUvjUUNhwHnDJIlQ2PP7rnDtDw=", "7p0t0+q+I4bRhKPedF4Y21/JNfBkydqw3gCcOQYjINA=", "ds+BlQpgcvOsxGl6JQV9D2u299hjgtLA1wCJxiNdKs8=", "x3ioJedF5+yLWPHkm9X03ssvmNt5mj9HQfjIJWqav+w=", "4GHUbDRxepDTmGkbPFWrIhi3/i8ENJNO2VoYXOc+60I=", "4v7VvCdycfJQDrBB11vTFfb1ByPlEd5n5Yo4SSIcKrc=", "WifBz0Wx15NUGr/VkfHgugN2I8sQFsrqHs7tahJ3OhU=", "JZ9vlMfVr2bi1geZX/PKk6kAWqfsBAuIcyir8YmlR4E=", "dsT2a96HRtQbnj2r1fcfHt9o16vakrxAgXqNM8/o09M=", "Cs6FGzHjLf5uE7G5c/GxcmTNkTW/9BlPV3zjM4639tk=", "ZapQ30z1uKLIwp2EdyTTxACHYUtD24b12fqqxRF33Fg=", "Ay7g9g4Fr3A0RSTHHhT+JLbeioRokgS1TJBCcrofIsg=", "aohd01bjesIfDJO0jG26AQpjI5O6YaW5kxBi7jDHiPU=", "AYh4/0e9nEBM9ywvVyn3B4sb9LzfWcBDDi1Sffwui48=", "iBn0QCZX+bmaweBXuO4FzVmKFlh3G3gO2BY2utU0wOQ=", "T6taIsX8lA+qVI98kplGgwblE/b4vZKF/l2PrQeuvI8=", "VVfqntOtYKrz0Hf2OcWVgILQquTHK3j2PAq14b2jAYo=", "2rSw2IeQe5C+2IlUqqTpaRCF37dnJY1tdsFSCKW9yDw=", "Mu9nRaZfVscMxTHBaoShBrVgsmYfmp9wM5ENYcBiB/E=", "Y2N8VM4vR5PBMdZ8hze5kxQvPeReAz+VzIRO7j+IBNw=", "zFiAtuALEoC/x5VdYQGtKc2J5brGFEsT1tQ92IChsnM=", "/5GYFS50VJ5ywQEcfusBQoWvLtRJsiij0p53qRCx23E=", "PMJ58LH5UbEKY3JtGmi7CeKUpVI54F2zaGiEhP6mR7g=", "S3ocHw8xKUtZ0bb+BKUMVNTrI1z09FViS+6m/MfesI4=", "eYxM+FJagMgvdgp6moq0+8pWpzZ3s6kPiP8xh2a/2oU=", "s/b6miXkAXlcA3kA65y24y2GXustfFimB5IHX0Yqm2Y=", "B5+gXf11Dd0zyscpV4iXbBO3gFYaNIaqgsx9CliYFgg=", "p5GBfxENXSQFEum+Q7Fu8A1PBG/8sLaTK0Wjngna2ho=", "xZIGPFgtH9Vd3euBf6OjA42Lp+K2D+YRs6eTNXBvFaQ=", "Cb8kS3MKnM8S7TTqLeeVaYQT/eE4LY9N9JjtY0iUmzw=", "e86ICtXXf/Dk6IEXDMtUWVpWfpu61slQopxJoPb4RA0=", "SqpiyZGotcGJvcXtJESBh+gFjH+ckwY60MJ/BgD0+zY=", "mItXOPDIjqTP98kRbpD22Jkrn19TLDBQB/D6XpOBNFA=", "U/aGsMOkbFri+LkjDo9HBLJ0gtwyC4ZGZG0R1m1at+4=", "D9vME2ZDwzlQEF74Y+mXmQLkuvGzXj7WkLRHqRqBBe4=", "oOnY8pVBe0UOP6zjlkMaUhr1fezP8GjDf5fsUJ6yXOM=", "0nizXD2P8qC0t2vA9SfPe6VBm8t3BL118M3Zx5lA1jQ=", "g6Ej5haRl1DpSWc2a/NL6nM1F292racVlf2sIlAD2CQ=", "z55U9ByXxcmu8hF3jX046qhvmCPBeuStbYdaB0zvtiM=", "pc0ZxgzfjxyfVIEseRQSCUq6i7OJSFim3cFdNZskZKw=", "Mz8v8Czl63uUi6zZ01f/7tZ10qgpIAl81sOA6ARw8LY=", "eaDX70NKWWU5+CNcpSeBc693PjBUjm8X36C5BiUmzM0=", "SUiT5m42Qp8gCbdjy3t/WR4gEnOhtLC93ec/+6Zx2sE=", "xgU2cA+xSk8uyEU/RtN8Uf4im6wGfWGqr8hMhX8saq4=", "IpVFcp/bQaAt6DH9z6/mVXL2wXWIZQwBSJJWPnUOaxg=", "Cw3j7VQ7/pQl/3GpR73mJ6P0xjJHalk6uzmqntAAtAk=", "nnGcBdOXhaulIp91kzRuqZdsaEjphWDtvWsoEidf4MI=", "mC0Zk+cXg5DxipMAYcsF+yQBbEioyBWPTD/AEBG5YR8=", "3PCVAtcw72SuMYAOlLDTd1lSzUQtYdJiXRus/xl8pCw=", "nrNjAYOKq6HoHl7OdEd2MZ+6Iw2PnGseYRnixBZd5ME=", "u/mFDMERzOomi5AsViRGPLW2gG9JaiMMvemGu0mCGNk=", "4K/poZsEUfcWMsMZ8OWIIa8ayEVNiTyS0Fm0vJktFf4=", "k1INwiOYELvG2MhQlEGhTLfnj8dcD3nol8XiBHoVYag=", "CkGmOIG1fPViHMlRJd0rB0mdn9NQCT6cbBC+uoWO/R4=", "DN1isWFwGQvqiPTZVB/SUBCTqWq4njwmrWeoYhksnaU=", "YJ+s05CZGVJbiCE0t5IfL0jqXg9kGXV9b6Q0SxkPpWg=", "FujKLWAeE9cm8j/Xzumw3W+uya1p7UKEsXINCQ9hn2M=", "Xme5XfHO3AQFHQN8RzY98SO0yrcpaDMVfhoaTE0i1Ik=", "m0mkRMEK6yX6Wyp5AHHSTj54uO0+4ciSDkS59jOwLrU=", "PaDicRixDHy5y3/ByujYKGRkN+9jfhktKadNoLOwj/I=", "k0PuVWGYSWLlzOq5gLsoGJCGMHH9JQ9zw6ok+hj4XEI=", "amrm1nhW9j3DIrstPP0BlN5GGNNRTonhfpWhJ8YiuvU=", "iORd8ljE8nFoWAqVWLSwZMgJYNrr8uqfri9F9ET0vNQ=", "pur5AZDM0dXEjL8Ih5ZrWGHvQF0qcLdg3UpBXUQdDk4=", "t0/EK5J4MKFh5IJGLU3RgB+LDldoIlNqaUVYPo4aAcE=", "QghJPUhDQVYrD3MO0YeTYkOYBNV0/pnYP4u/S878YPM=", "q9O2MkWNRKT7MRyTDdxLeeT4J7ViSPIfnMlplVzq6EY=", "hwARRz8yH317Y/bx7noYZbkzg7GE8tu8N1T4vdbel8s=", "RRtbShL+R9m0bOa+3rjapVUtIJ1BSnx0qDpJHf+IOjw=", "2lKLD6fMqdE5zl1J5kU2Q2Kz9JD2E2wCqc+GKQPkEss=", "EjFFnz3LDwj9LWzKF8LfyQDXgNAhCAjF3PMqD3uv5BQ=", "dqnTr2LASC5MYnlNJF1b4IR9mVcuncBwsoBocFBC6j4=", "YbtkD/xW7hGI9cijGgpYq1N+N2ciyIV2VntjZRmYIE8=", "dylh5VLvunt206u8mpPX+gVOz4FMaDHwIZ+vj8gG9Gc=", "2K0+QYrFY3chV6NtCzo8SOfZOdb81Uk8MCdugrvy6d4=", "YIJuC1xUWWAwLh6eXK2doiQY4uE4xnPPlXQaXdqP/gY=", "Du09kUTtakBgTi5xlrV33WznplyN8gZSJrdZdRST5cc=", "VrWvaRWtxflavlbMLLBGjI2pck93OiS12g/DgxBNaZ8=", "/nZdpiGnCPfGCYdOzYEvnqjAz++TV6WjceLUaHwVdoo=", "SnNQcNesyAHscNKepJgzsAVQzaf+aKyXqMKEptoOFX8=", "w9GR4q4YSNmvV2bUoDnWx4FYuEBwlmk/tCnthI4L9po=", "NJBebqGkhT9DdIigj6yZfgDGpHWBl3Ro7xOYh6J8/zM=", "2TZimQEZHX64kNSFhrzELkd3lUSiZSAgq3otGHIv5is=", "BRUAoYz/qZes0/IM7FcfpTDEgsevr/PmueXk7MTax3Y=", "HzoiXiVP2Oey0gPyh5rbW3CUcKrk6VDhVr9issiYFmA=", "tTWp/OCo2zERwEKnwJJvyIa2rvmpIy9a+l+/9RjGlAc=", "QHtx7+6xRz/eCk4tJwHWEpwa+MvRfyL1Jv3aAA+MzoI=", "b5/rSi2oDqWtlT5paHi5230mULFwlJB6y+2eXj+6LBI=", "dsIl4rZRz1bmqX9/Y1N5NjicC3SjS40NmFhC9w+PBOo=", "rD4Q4kqxvquvCZzZ/3Fs9jNOgysTeVWtZ19Lkq7/FAw=", "sEN/l3Ntb2ftOaK3nKaE0oFPH54s1dX3CBz5P+1RrU4=", "XPNjUHrDMWWwz8dhbNNGiETGQ+Ta0C4vQiSxQJKFucg=", "26dETnAtJT5FMbF0Jo+wY1m5pDouTB2OvuObeHdtv0g=", "phTbQs7+bXbtNqtXkUNEzK2UgFuICyppkoL8HZguiLY=", "rHTLma9t6f6W3+gkxYgk1n9BQ/vMVXn6AKa2zazfapE=", "v/IfKWhkUqiNmuNeB2ykRU1/PzTPhgK/ayvVrrXTn8k=", "s/c3hHXA4PVXoxEDUGmcAer0ZUeQgFkZzDn2pa7OjKo=", "VqbxG7lvotyal9tx4csSJagDLMVVJWCIhlHFHrhf15A=", "5fDct/rWFddvCAh++6nGfpn62wk4a7Sx+t+CbHHTmwM=", "9hw/Jn/fDFLJFzyuZeg3mn+HmJz63GuPPhFsK619b3s=", "HL++O63lupXn4yhD9h688+uZVenzKXdi09OgzgDW3UY=", "zaOlm1MP/EBB7Afi+ZxaX9rv0Sq7rpAAmTMJWPDZ6r0=", "1s4DZgUh/K3KpiZ4wFJ5GC22JbCQixjKcIkkWPzBOk8=", "IZBTyc0qzmdGnsBMNMplf98t3rtCZT5BKTWKnal7HVY=", "Psuc95QHw3FlPi7y8qFO1FLgvtnE0751Q9LuGaroUZQ=", "l7SDXOAR+GiVJxqS/r7mGO1nAQ6pTBd7YV9GvVd6UCw=", "X8xvzN/llsNdQ1MuTJfLGcUrx3IHRIh4XeiHmkA31ew=", "C4049hfFKT9v2/XZAH9jalHMq7wrTnM5htOizU0U/oY=", "kOT8cJ2wgJiPUq7t66uUHUsooSl8w2Cl36MtSQboT+A=", "GGExbDRPesawWSPGs+UloNXpn3pxr6oKsEjRJ1U0gBE=", "b89V/ltUJgg6/Kf7E8AgRP0uqL2Fm/Tfj3xuYKkYq9c=", "ypnuxZjtZLY+/Vc2j2kZ2pOX1wKk5IVXIEkdntVmlug=", "sxHV/ZSa/1a9rhzqWwvDD9V2Y+2h1bTcmcQuJtzZx9o=", "iL06wptDq9rwz/4S6xMZ9yCf1yCSjljBkGBSIXHMHqI=", "G2DBNI2AyPnBFCawKALM6Akl/4MboE7ZFzpj5adwCTM=", "I5G0q6f9ih2Ktnju6BR4b8tRak90JW8Rghpdq9OWa5k=", "ovai9LPms4xcnOrYXO3TIKEGITqS9E2VO6cmoWLkyiU=", "8KSa+Lx0vmQTOhD4s8+2/yxNfV9kFPemPdOny2Now7o=", "+9xuOud/TYhyuWh9pZk3aNye+rnNBqCmrEA+fqq5NKo=", "i2R6ODXFyjs15K+qYq9wxhzK/aCaE4MjXSsUKj5dpGQ=", "FA698b2xJqeWQfIwq86JWTnUkScFdjlZI7DBRKii1es=", "kF0HprhJDywQzkwf9tuKrdvcPRopDeLZisJJw5xKGEg=", "Jr1XsOiUWcdiaQM5vI+BnmDF9oAKVig2vAPTxeXHGsg=", "NbTFVRfunGYZVjimPHGajyGVCS3n+dKGSs7TdNZgm+Y=", "LnOVJaD14ydDtjPlLnHuLDSaafMpfnUijKwwzXk7FYI=", "5vAZbKHXDRtgBrU8M+UcCu7OolFh7pyL6XfUOADSet4="], "CachedAssets": {"ddDfcoAECv+GUOGsgEXOy8vQZFt3QeST/PHn1flxsKE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\css\\site.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kc2urnfzw5", "Integrity": "nDHJdDTU6Iz19np37pHzXUkRFktd+Xe1TIcSYBSEXng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 1723, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "K2nQLchwdXSSv+X0lB/XiAKyHR4jGSWi+rvC8tq0X4k=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\css\\demo.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/css/demo#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ok5r7wnkv", "Integrity": "6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\css\\demo.css", "FileLength": 2514, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "s8SjezCtAodYvnOW1VuIklu5qTbq/GHxrksCISHaylM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\ActivateEmail.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/ActivateEmail#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wf7zkmmsg2", "Integrity": "lON53pF5QfXQPeynlyNLW20fO1Jm3CHH+rwkWshmpjY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\ActivateEmail.png", "FileLength": 45815, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "7wNEWTTP9j1cYz7SFKDJ1NOhhEFSsvklV46ontQ/fEA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\AGB.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/AGB#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q7mriwkq8p", "Integrity": "NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\AGB.png", "FileLength": 73761, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "rDWXzB1jprVi/g+vtUe3Lsz9omYtcp/q2edK4tW2UoA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Attention-removebg-preview.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Attention-removebg-preview#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0eoyn9ev74", "Integrity": "+Io5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Attention-removebg-preview.png", "FileLength": 17077, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "o8N6pjlw9AtPFjE3PBEnjrHrKUN5KFj7mCWfm6AXuR0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\1.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/avatars/1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5dw95i7i20", "Integrity": "GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\avatars\\1.png", "FileLength": 14015, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "M9LBXJVOzB4u5E7EcgwFZLf8txssYuOGTb9vZTXzP/o=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\5.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/avatars/5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7wk20hnyd", "Integrity": "HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\avatars\\5.png", "FileLength": 20488, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "6aENFBrWoQXUGE+DuAQiIT4RjWufT//JMsatjW+sBdw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\6.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/avatars/6#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "laj0069a7v", "Integrity": "U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\avatars\\6.png", "FileLength": 15198, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "YEzIeOvcgR0ZQWvtveeV9GIpVFTOzwebi4XZABt7fsY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\7.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/avatars/7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jzhuf0rlpo", "Integrity": "ceq+EmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\avatars\\7.png", "FileLength": 15180, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "0X3UFyiz9u9XZ20hBjJ4xYo/HrCq3ooqp1/BSeIVGJE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\backgrounds\\18.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/backgrounds/18#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zcrq1vg6ym", "Integrity": "/fVYHI8QJL+aw0jk/AjqF8Q8usxEunzAwNftu49AN/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\backgrounds\\18.jpg", "FileLength": 88783, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Q5WYa5ADEeyH/8WYMM9D86W0lY2Wp21prNIVkmSBIYY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\cleaning.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/cleaning#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q6iwg7513g", "Integrity": "7r9n/l0ePgee2rx7jPgsDkH7+zeuOLGVqlySnPuze/w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\cleaning.png", "FileLength": 1739, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "PDNDxRbJoU2KJpEh2+kzNsYS/NSx0dbwMuYzvpR8B2w=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\color.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/color#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "66as5qo0c3", "Integrity": "fm+qDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\color.png", "FileLength": 1203, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "02orfFK1ZHBETTPHdPknkFDUH0zibFRf4WhZ/zjVdIs=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard1.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Dashboard1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6735s5w30p", "Integrity": "jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Dashboard1.png", "FileLength": 152669, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "gt8tT3t23LN3wvVMY96kKvnkRJKSa2PTT9Ms+p4oJbM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard2.jpeg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Dashboard2#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oq5d0n04gc", "Integrity": "UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Dashboard2.jpeg", "FileLength": 51207, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "tQurZPS+pOllw2bucHLF9GgPn9jwbU9I6en7UgSadDo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard_01.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Dashboard_01#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wf2p4xcoui", "Integrity": "nEBoPob46ocCh8FvwhNE9aCwR+ZIDds0r+WtOJJISvo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Dashboard_01.png", "FileLength": 5418, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "hjShpbAkV6VfqLEoZhKV6DapbyUXWtCedQYbMDVVzUE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard_02.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Dashboard_02#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bgu0r6dzip", "Integrity": "K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Dashboard_02.png", "FileLength": 1989, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "6sH7xnS0zP3g6x0+nlRQ0Thehr2p8nLgoW4DNGxLtKY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard_03.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Dashboard_03#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dr26h7x9hl", "Integrity": "edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Dashboard_03.png", "FileLength": 3566, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "tsLCZ5znWELi6KZBuUjtzZfazMrSOtwDPPc5tdAQFMI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\de-flag.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/de-flag#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xjszvk8663", "Integrity": "szXHI+v9CKe9gq/sSk7wCT3Ff+xiY9WkgU9T3GkN/Co=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\de-flag.png", "FileLength": 20056, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "XrAgVcyL/KgRI0cHF9DXbLVcigZjFOgNgugyplpbG2A=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\1.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1dcgywla5o", "Integrity": "a6t4IujkLq+637JfyvpU/86ABXnFoB8MdTUNkb6kgRI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\1.jpg", "FileLength": 18135, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "7vUiMO5juOa+Wn03kqc4X+QdnhV5710Hno7uAjehClk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\11.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/11#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uj5sfxbjnm", "Integrity": "5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\11.jpg", "FileLength": 19087, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Cl7qjaOd6BHbiSZW/gQNnx7UsHY2LtnKM5zxyYCZJEM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\12.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/12#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xqqhdiskkx", "Integrity": "qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\12.jpg", "FileLength": 28075, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ka9DuaK+5gZyxYN0b+yL24oNeDMXjL0muV/+hayGV/8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\13.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/13#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sqtpmqt4ko", "Integrity": "LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d+sCErs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\13.jpg", "FileLength": 12929, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Y0H3RuzdY5Q/z2UfKkiclPAzNTXCvOd1lkpOiJwlRhc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\17.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/17#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mbfplb2995", "Integrity": "0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\17.jpg", "FileLength": 19716, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "MrzPulDvFRePcMAAFzF0l2X609D7k/8mppXu07gn39o=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\18.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/18#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4kfik9hhpd", "Integrity": "RMeMm27YNLobckPL3MTiDAEeuUKVF+/AY4KPuNp+cwU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\18.jpg", "FileLength": 31223, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Xgd0Zaiji1ndYb2H075D0fa+vxIwRJ+wGEyRS2D+cuk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\19.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/19#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a3beqhyfaj", "Integrity": "WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\19.jpg", "FileLength": 15553, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "fxNJXt1I/a2lphBc2vW0p7QasAzboNp+E0gIfc6QbJ8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\2.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j3cw3e50ho", "Integrity": "wc+6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\2.jpg", "FileLength": 13332, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "aCwMuy/NBTIOqe/AjDI4AsNeDsBUG8MyrWZu1Z6rQBY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\20.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/20#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6ffy4hs6lt", "Integrity": "kipYcAx+dLYKPwZ98vbg++WhjJgPD9C+aVL0/wOWyXI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\20.jpg", "FileLength": 16593, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ddclm15Vl04xsP0QZJ//hR5x2ZcjaUAeDc2iPeXh84s=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\3.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rga7lkqrdc", "Integrity": "u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\3.jpg", "FileLength": 24285, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "x2E4QJRiktcYK+lQgW6AawNJcky8pTXkHYU77fSYu/4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\4.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/4#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tv0gns518i", "Integrity": "h6CzEKD4MIeFaGkx+b0QuMkWMTa+YkPPY7/hdZKSjG0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\4.jpg", "FileLength": 22875, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "wRpo54BMU7wMy3NDFaPb36Wi2qtwQohS170GDkjuvMQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\5.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/5#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vk7ev4b9zi", "Integrity": "IePvmLa1hS+rQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\5.jpg", "FileLength": 22938, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "uIa2FvcuYPAK1ps/jBI/gaojpBvMVxNjX145RAR/6yw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\7.jpg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/elements/7#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n38sfx2p3i", "Integrity": "/fRu+iZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\elements\\7.jpg", "FileLength": 21183, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "UdRbestliBJqw2wfhW3iA6yo6qPBy1PtAVb0pInPON8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\favicon\\favicon.ico", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/favicon/favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2hcxmlxs2r", "Integrity": "ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\favicon\\favicon.ico", "FileLength": 1393, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "JZOVpWOQ1yqMST1yE5bCKp4IEj4Ej5LGG0C0Zh/KaCU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Foto_Register_Form.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Foto_Register_Form#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aoece3ix9v", "Integrity": "thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Foto_Register_Form.png", "FileLength": 90383, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2hQgKC86bAYpft8RWeXS9sqWhpmccyQxl+2vSJVWkIA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\fr-flag.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/fr-flag#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2zu6d6aky", "Integrity": "DQdfguK09Pp2IQGulUiKQl0t+fQMW34SCVRQnGNYeb8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\fr-flag.png", "FileLength": 16395, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "MevGEOJVXC/DH6pKjZrILNjwMQ8KlfnBQ5uYrJNU+Y0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Gekauft.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Gekauft#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rlsnhgmo07", "Integrity": "j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Gekauft.png", "FileLength": 8670, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "4/Dz8wyZDBJCqdzPzISg6b7OGLXpwd36qAq5Y3ND4VE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\gisper.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/gisper#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c7xywxqxv1", "Integrity": "mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\gisper.png", "FileLength": 808, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "6cgiNws9lpzhqnSgjegeJ9VwYGkyptiaVkY1658fgZo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconfacebook.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/iconfacebook#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dtpc2iklyd", "Integrity": "sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\iconfacebook.png", "FileLength": 1316, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "7drLw4rJch30WAw+pw3X3g7/f54RVHMGDdfWEJ/GvEA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconinsta.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/iconinsta#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "g2myl4c9mh", "Integrity": "OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\iconinsta.png", "FileLength": 3489, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "stxwkZAvOL50XuTwMipGHl6hijL14Au+5HntWu1SYzE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconlinked.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/iconlinked#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "64c1t2dskq", "Integrity": "lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\iconlinked.png", "FileLength": 608, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "CM/HNp7fxTMcF8sGLeeKhwVulMuujlus7Kps3eGsDhk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\asana.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/asana#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m8ed7lopx2", "Integrity": "qtt8Djtym3fWp5n+PpH9HVsMcfniOIs8KlSEVvgMFcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\asana.png", "FileLength": 2236, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "poLopRO0zPTAzU/AIemfmJXVjnwL9xpvLJHa3CzMExI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\behance.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/behance#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "coaxahx6t3", "Integrity": "tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw+5ck=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\behance.png", "FileLength": 1731, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "11GMYeTuO7Gy29OraVbJPByax7Q+z6EY+q62LNr1WSw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\dribbble.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/dribbble#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w706m358zy", "Integrity": "nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\dribbble.png", "FileLength": 2848, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "fNApgZFAD/ZcRgU2r29A+EeI8quYeuZEI76SQ30HdqU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\facebook.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/facebook#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h6xi8zk54u", "Integrity": "h5NQXedd04yWx+AtNmndoyZpe9mF71FgFrcv2/XGHIk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\facebook.png", "FileLength": 681, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "i3iv0lbt+/hnP18yqVZBVMq9/7g+blylLUv3+Cop0vo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\github.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/github#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q7i6lubamj", "Integrity": "SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\github.png", "FileLength": 2169, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "/BZcIuZ1MxVSYsDygzZGT4Vjc0TZVfkjW3ccYyVXp5s=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\google.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/google#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yn247w80id", "Integrity": "fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\google.png", "FileLength": 1932, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ocAYIuo9mPLVSDriM8UofanUlxFuHoypY1206JQ8TAw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\instagram.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/instagram#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t9b1hn39ce", "Integrity": "rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR+bg/6Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\instagram.png", "FileLength": 3128, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "LoPu2saB/JZByQMXlxAmFBovNd+T76MkbdKHeky1+dw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\mailchimp.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/mailchimp#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37ik0yzl65", "Integrity": "H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s+ggWOtxrWNfzK4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\mailchimp.png", "FileLength": 1405, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "IahLfBA1w3lqF8WoHFjQ6CoXgvtzctrxuRZ0scb5/xc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\slack.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/slack#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w43jfux8yp", "Integrity": "oE0mYTJAJstA8DXOzLlAUnWFXyWFtU+LhHcbRcjs0mM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\slack.png", "FileLength": 2550, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "vz1PWPp07pxhtu4vqwQ90u4ACQ87FkYohn4iePcJvjQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\twitter.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/brands/twitter#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "btqwmt39wx", "Integrity": "vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\brands\\twitter.png", "FileLength": 1564, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "BKmfvmwERUU1OXxvMvVX7phhr5bGxw6YK22kv/Cfm/k=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-primary.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/cc-primary#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7cz3d43tgn", "Integrity": "pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9+0juV+SQ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-primary.png", "FileLength": 702, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "PXj6IIZQbZCX2i37vcYNdaiJ3ZyKzoeOVUKSqRFCNa4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-success.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/cc-success#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sozk5ui7zb", "Integrity": "rC5AxYDuZkEY6+UekvJZ4HJdxe48g5byrHETLimP4Lc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-success.png", "FileLength": 776, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "PbjmvJv0oHLGElX3IOu/aRwdt7jabdapmkuJUa0fkN8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-warning.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/cc-warning#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yqdeq55wew", "Integrity": "5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-warning.png", "FileLength": 689, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "t37KCCJaJBcIdGzqVHd473NOApbDWeLb0b3z0FcSzlk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart-success.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/chart-success#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9zqdfavt9a", "Integrity": "1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart-success.png", "FileLength": 1528, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "CDCitGyaYw+JZfdRXoVs5J3JoPzbU5Z7vTGK0UJOunM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/chart#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j55jf7gsvq", "Integrity": "KXQATGROWvL9P+yaqtuEY7vfQo711XTl3mY+X1UTsYo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart.png", "FileLength": 1491, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "GCRqxmWFGXzTIpgosIHnEgHprS40sSQA4qnJGXTv7BQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\paypal.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/paypal#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yw16yx1qqs", "Integrity": "MmXG7F0O5+Y2+eC/my8aBVs83MestyL6XZQHgb/yzvI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\paypal.png", "FileLength": 1090, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "AKI7mIEjH22qpt14IlIpweHYbseiYmG07RM7GBzbDs8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet-info.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/wallet-info#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uyaw46hsrb", "Integrity": "hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet-info.png", "FileLength": 936, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "eSjGJkAeQt7AYvdU7mtk0cWf3LZEKUJ7raYSy+eU5lk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icons/unicons/wallet#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eik0yqeyxb", "Integrity": "Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ+uhFMjuc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet.png", "FileLength": 920, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "DMiQUIRMy32HMGlnfjb+vXHOvL5q93J/uBq+6OIpkSo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icontwitter.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/icontwitter#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "stbbn09hjr", "Integrity": "oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9+wfmmWHE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\icontwitter.png", "FileLength": 1834, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "zn9Jlp+/ivpnJnKPtllC+Rgwp2GYgjzTqTkTgP8+I+Y=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-doing-yoga-light.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/illustrations/girl-doing-yoga-light#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pqilfow7ks", "Integrity": "IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-doing-yoga-light.png", "FileLength": 219096, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "8hZsSdGd9UUt/Zh4DDo4iCs5W7VSaj2D6YMDvtzRUJ8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-with-laptop-light.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/illustrations/girl-with-laptop-light#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2cvzwvwj7q", "Integrity": "nrpYnyWa0DzjPEfjd+RkI9dyXy9Holiw4FjsD7dGLc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-with-laptop-light.png", "FileLength": 183173, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "7wrF5lHMCIDz5m6etPba0xTXam0cA1uWBiO0ex3yNoA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\in-prograss.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/illustrations/in-prograss#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u5yfcb6n2t", "Integrity": "Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk+aKWCngXgs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\illustrations\\in-prograss.png", "FileLength": 173307, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "kOm1f+pg1878y7bbIEcCz2K05N1vGiDxvjEnCO8HEgg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\man-with-laptop-light.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/illustrations/man-with-laptop-light#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lbgi9tpxrg", "Integrity": "ITDy9whlrMB5qxY945SKc+tYgjQxgFndh3dqPZzC8V0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\illustrations\\man-with-laptop-light.png", "FileLength": 8826, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2YhLd8rPiqxLPJMtT56o9wQJY7YRDC7LSLyoVkiYi6g=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/illustrations/page-misc-error-light#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4yk20wtg5w", "Integrity": "dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light.png", "FileLength": 139086, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "MLJ75ZtG9W4DADzg9LLQjALKaMr2HBGKnSHjaYrmox0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light2.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/illustrations/page-misc-error-light2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hu89au1p7j", "Integrity": "CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light2.png", "FileLength": 216684, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "HLSywy370E0uJ4Qg+Um5jYWslhQQFEVRNCcmsL7hRCQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\it-flag.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/it-flag#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qssp6wqowt", "Integrity": "CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\it-flag.png", "FileLength": 13908, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Hc8gAKbCjRhjRw8jKLB9LdAi1qqJ4XHdh2PasfMmD/s=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\language.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/language#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j2dl353fui", "Integrity": "lcNl3e+JH+C5Ey3+BNS9kbCRVmYHFtRCqf/cje/+ZpQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\language.png", "FileLength": 9250, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "caAkhR/orBM6N6e/ZS43w4fQHGtPMnDMiwa8iVG7glk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\lock.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/lock#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qdws6rar45", "Integrity": "EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\lock.png", "FileLength": 8410, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "XvVOF5FGZdh1PN48PVjvZo0LV5em+vz9HkRCCCcZyIA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Login.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Login#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u5ennpjbay", "Integrity": "ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Login.png", "FileLength": 1890749, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "5FX002MPhrLy/XIe0cc1fe84issfP7VaQK0hXXu1BK0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Login2.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Login2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uvazvaoys7", "Integrity": "JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Login2.png", "FileLength": 390213, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "sKslpchj8it3oS4LCYpTXW9v2p/F3XYqFNNKWa82OVE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\logo.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "95njsei89b", "Integrity": "E1C5YkxAxHD+s7KAP4l4wgPVe4EhOkWrhwmYfp31lRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\logo.png", "FileLength": 48171, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "3s+qiHbF0Y//Z9yMdu4hnSaEXFxH1c3GRkvRGtGrqzQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\LOGO.svg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/LOGO#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ftm9a8wvm2", "Integrity": "wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\LOGO.svg", "FileLength": 103145, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "A+G3Mmlk0Nk/HiN1/qzwODRxohwcHJ+nPAwgwVAx46g=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\moving and cleaning.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/moving and cleaning#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h26kxx62xe", "Integrity": "R9+k3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\moving and cleaning.png", "FileLength": 2571, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "97t+GBnMUQbZepRBW5psqY5E0di6aJamUFfBCVG3/lc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\moving.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/moving#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "08zxpr0k6o", "Integrity": "cMrkb+qVwGbDhAEsk4jQ+QwjJP3kglgmXwa1RHnYHM0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\moving.png", "FileLength": 2074, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "TMF6NfaOiRMJJt+ALfCEpf3AHqPBaS4IGVGHoBNX0xI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Nathing.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Nathing#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6upc1whwmo", "Integrity": "MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Nathing.png", "FileLength": 9265, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "dhvNuvru5hzgUwL2A2PCwmav63D7q9eCNFCt3gP0JH8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\painting.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/painting#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4nmlm6ldy8", "Integrity": "IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\painting.png", "FileLength": 1571, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "mp4BZXaTux4lj7vvWplXiGwoCr85qVNasy7QDL5V3/w=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Partner.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Partner#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oiiwkv27sk", "Integrity": "QIqgz+mHXZXoeh4zXnsGSo9coUm+5Kit5w3vbSpA544=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Partner.png", "FileLength": 52265, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "6pnTURRqSPEzLMiFQ7HIDMbkQKaFGUF4Qzip1eCIdZ4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\pizaa.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/pizaa#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r9shujdryk", "Integrity": "obyR/G5Lx8H0lsDbHOhl0gVCl6Qu+v4MXzSWeyFvzCo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\pizaa.png", "FileLength": 49796, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "5UKlQIeLMklMW82n06CpEnc1jUTvQf1CLC6vlw2YsyU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\PostFinance.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/PostFinance#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4hsu3t0zv7", "Integrity": "qCKlVCPPZaUg8GPlxVL3MP+kwsFzZ0rqx1DtWus+VIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\PostFinance.png", "FileLength": 3774, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "fOnoiIGGqOCDIaSomiuChVwmkGreROjOQIdsvXeIQcY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Post_Code.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Post_Code#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pgsifodmme", "Integrity": "mX/4jdy45NsRcjHViLVn1xne+qFNQA+2eu0x7sUUC+c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Post_Code.png", "FileLength": 2469, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "EjkuAe1zhQaammDeiAxXasY0ZFlS4Epb+r+4QllmJu0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Saldo.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Saldo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lkqj4z6h2q", "Integrity": "u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Saldo.png", "FileLength": 4743, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "K/BfoA6YXwvLkh/Jck6NLAQ/akD7E6Gm76vGkrcMJOM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Schloss.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Schloss#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u42ytrqwrl", "Integrity": "spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16+Y4ev6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Schloss.png", "FileLength": 2413, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "vo3yQqNuJq5mCsZp4CDiR2Am+yctZdXWgS4XK5SRDMY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Stop.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Stop#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "i5hczuhaqb", "Integrity": "KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm+LFHBs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Stop.png", "FileLength": 7615, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "AiYWHnRvIxD8YnaaGv8+TwH/kwBjuGkAXvBITXCjJxA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\thank-you.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/thank-you#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "22o4zjfsw6", "Integrity": "No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\thank-you.png", "FileLength": 968719, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "WtSgu8AKSma7j7iegeZgktq7b+aRFhRhSFYn7QOIi8k=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\thankyou.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/thankyou#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06v2xtonbi", "Integrity": "SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\thankyou.png", "FileLength": 75950, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "kQcI3IPByjxROusSRONt8HdkoPHLyWp0W7gQefvnbEQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Twint.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Twint#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xag9m31aj6", "Integrity": "yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi+6TwRbWtVysetM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Twint.png", "FileLength": 1882, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "LuVmhUiefqB0Zf2W7uXtrv5vtiTCfv3t4roLsuKUMLk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\us-flag.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/us-flag#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6rnqyqozqm", "Integrity": "wkRNKPLm/0134O8A2OXzaNfAFIDTxFK+eSQCEPhPsB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\us-flag.png", "FileLength": 24631, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "oZ2t248iTyLAfsOS5NdADGgGHZeMwTIh0qIha2TnyWo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Visa_Konto.png", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/img/Visa_Konto#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6yr6353tw7", "Integrity": "AqYCkkCKKf2n+zRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\img\\Visa_Konto.png", "FileLength": 7240, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "B/KlJQdvvH1v7S+ocbNBRFgBzQHlToX1csfrEDkCU08=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\config.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/js/config#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jrtn85pb04", "Integrity": "b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\js\\config.js", "FileLength": 753, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "F1GYbxinqUDPkMf1Hc6qgd/omyCdGijTHnt/HV9r07M=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\main.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/js/main#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ey5eontg2m", "Integrity": "prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\js\\main.js", "FileLength": 3789, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "RS54BwTPRPXzgFS0w5S3dRSGQzHURVYUykd5KpzTR2A=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\core.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/css/core#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z666vu0l64", "Integrity": "21uvDVBawlNTpG+D5VMfC1bb/9QhPhnjE1ACKek8FMs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\css\\core.css", "FileLength": 986733, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "/0wBBv08SenfbzWTZByxjY38EAm53eusFACdraqdylk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\page-auth.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/css/page-auth#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mnmed00ooq", "Integrity": "MpcNIJxfGjwnslj6av+r2qwj/dzHMiSkOfxbc5VHFOs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\css\\page-auth.css", "FileLength": 16195, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "du1+gjzK8HNMeUQgtYqqTbf3NSLPp9RLx9mgSiZpG/4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\theme-default.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/css/theme-default#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dk2ytlcajt", "Integrity": "fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\css\\theme-default.css", "FileLength": 74010, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "3uq9VkrP16A4iTXCCROPuyeCl53SWUVHHIIYd7F128s=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/fonts/boxicons#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4hzulchcxt", "Integrity": "6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD+ryrU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons.css", "FileLength": 79999, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ZVwITI56VGEESXTKdiC6VsolZbS2Ckp9D6JY6rrrwX0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.eot", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/fonts/boxicons/boxicons#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rri4hp3ggs", "Integrity": "Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.eot", "FileLength": 292572, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "FxBnp6n5/t1J5JRaGHgv8rWXW8wzscc3DiKjah8qCmU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.svg", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/fonts/boxicons/boxicons#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x6kftl55k2", "Integrity": "+VSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.svg", "FileLength": 1125137, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "3vx5ph+DLM9d17PLXocSDZWPv0pxRSFWQyXEmkZqxCY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.ttf", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/fonts/boxicons/boxicons#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q9t8biukf5", "Integrity": "Lpkz1ATMc/40f+ahhgq7eGVTP83nN2WgwqKSph5k2wo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.ttf", "FileLength": 292404, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "p7pK83kyFaZxq9lDJQqMHNyW3n3b8ZGgAHWNmHtqfZ0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/fonts/boxicons/boxicons#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1oivsu8mcf", "Integrity": "QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff", "FileLength": 292480, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "6o77aLb2Yi5PpLxlP0Wn+u7JkBdUpyJf4Fb8B+ylalg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff2", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/fonts/boxicons/boxicons#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5j3plm5gu1", "Integrity": "34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff2", "FileLength": 102988, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "C8vMfFk7Z6/kz0WrUiBtYBrU+JNi4p8WgNspkVVs3JI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\jquery\\jquery.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/jquery/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zti25kgd", "Integrity": "cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th+Kr6HkYM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\jquery\\jquery.js", "FileLength": 832977, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "FGPDp4BB/ppEI++h+2Z012estiIZIniG16y+7Peebnk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\bootstrap.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gv7dia6x1y", "Integrity": "5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\js\\bootstrap.js", "FileLength": 114929, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Zvs2BAHZOx1fftUThFAr2awu+H6aDSskA61lUTkbses=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\helpers.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/js/helpers#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nnhel8mq3c", "Integrity": "HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\js\\helpers.js", "FileLength": 103079, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Ke8A+pWEvnNLvPiacatMYDgE/gb9/q4lig6uSPNT3E4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\menu.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/js/menu#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9h18ncb23z", "Integrity": "g++rCLb8zL7EX8p2ZBiNPm/M+TlZA75BaVzDqpvDIVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\js\\menu.js", "FileLength": 80183, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "nIXoHcykqE8c0NIs7mXRM6yfhJfiXks0a1CUjjgg73Y=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\template-customizer.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/js/template-customizer#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3r8b2a0bt1", "Integrity": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\js\\template-customizer.js", "FileLength": 3, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "1RoT08k0FeCvibBTxk+UdWqCwbKiFRE60CKm+d+Gs/4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m8j3cetw7e", "Integrity": "Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.css", "FileLength": 5008, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "3kHB+841qOm+3chD6pfZVVz3MZT4OvSwVUeJ/U9jNks=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.js", "FileLength": 108747, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "xC1PD444i/s/tkstIgnyF8Ixt0H99yxTw1wi022aR6g=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\popper\\popper.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Dashboard/assets/vendor/popper/popper#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lln95dcem0", "Integrity": "R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard\\assets\\vendor\\popper\\popper.js", "FileLength": 57748, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ereWBWei3J1uUGrogp/P+WT0XTey4/g1YY8kkTvLzZs=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\favicon.ico", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "VHoLU+fj4oFYNEWkMnXrc/RZzI+zNDAQRjSzKXGdnnY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\js\\site.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hi2kjbe51q", "Integrity": "uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI+7fhODQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 1456, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "IFXCW5X+uL3oXJztsyS2XahHmsovUtFxdEwxcR+ZkZk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\flatpickr\\flatpickr.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/flatpickr/flatpickr.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gno4lkuonv", "Integrity": "NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\flatpickr\\flatpickr.min.css", "FileLength": 23051, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "wYVtd7OtV19vIJbrahTQlNSYj+6WHDIDy3Sw3LtRhqE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\flatpickr\\flatpickr.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/flatpickr/flatpickr.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5i5ypew3r7", "Integrity": "oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\flatpickr\\flatpickr.min.js", "FileLength": 58061, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "jeZko1lKabKIva/TK3DrfAmDvNH4wvu4d9YjSUQgIbs=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-ui\\jquery-ui.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-ui/jquery-ui#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "89idfrxomp", "Integrity": "rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-ui\\jquery-ui.css", "FileLength": 43221, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "h8hWjljxBNuo5LWuOJP9gvkx312zfBIqa52irzzN4rU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-ui\\jquery-ui.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-ui/jquery-ui#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5tbpznqf90", "Integrity": "CSeOsMhq+M856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-ui\\jquery-ui.js", "FileLength": 549952, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "elFWxDVifVWJ2401dF1e8W+RO/qm5NM97IeiUclU2cE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0td7jq9nxb", "Integrity": "XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19366, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "S/WISznWoIT54XIaYguqRbbTvRU6Yj5r4q9/8wtAXR4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zsyoy62yqm", "Integrity": "zV8SHd+o2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5868, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "oSjjRzxKoLE8AKVvVhASfbcIAr0lmhSn7LwOaxS9UHk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4ez5mqxv2b", "Integrity": "bJBFwc85FxNITit+4+dmzq5fRpbBEauvU+1eI+EpziQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 575, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "JLYgc/neZxmb/8bie+9b+zpVeONh8CtWzTPYG5i1HnE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gcjdx5jb8l", "Integrity": "0HeMWyQUbTbe7SGnSYbjj9+HVA0hKDmEUtbYoTKe+Bk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 51466, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "cQSp+gRAEfCQU6wa5w07fltaAZM7cP83IaAs3C81eI4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "35gxhxa0gh", "Integrity": "90Rlzuz8pIZK4g9o2I7nGK+9n5cU9Rbdt4GtxRO5arA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22174, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "RgQ9IZYmypwcgGodtoS4A/zut1ICf5h2nsVo/A+pj08=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "dfKEycahMfy5gyD3jkJSxr+7Ac6QalfRWbPuTCpO93s=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "JM32C2a0IjH9n9I2ujAjjdmvCA5iA8qtoT/DjSDux6M=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xzw0cte36n", "Integrity": "85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1095, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "gTYuBL9u+EYhMqVYg6GVpXbIAjan3XXj0wsFuF9abwE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\sweetalert2\\sweet-alerts.init.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/sweetalert2/sweet-alerts.init#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1hy9sozciy", "Integrity": "O713m4S+Nla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\sweetalert2\\sweet-alerts.init.js", "FileLength": 5186, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2gxG8a+mGxHfYMgS1i3kiNCndzWcmT6i+NNbOnaQMU8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\sweetalert2\\sweetalert2.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/sweetalert2/sweetalert2.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o9isx86jyx", "Integrity": "hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\sweetalert2\\sweetalert2.min.css", "FileLength": 40533, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "q/KDmhdxlqqKP9JywpnSZ+FH5/EyiSiowDjE0ry/IDY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\sweetalert2\\sweetalert2.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/sweetalert2/sweetalert2.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ka04m46apy", "Integrity": "hN/E1DmwAKy7yKvTL+rrKp+5jow0GskgNjaiFv4bGNI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\sweetalert2\\sweetalert2.min.js", "FileLength": 79229, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "4uyVLimWfZeRX6SwtmXoy0vevxgaBq1a+xtLuZ1MxDk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\icons\\default\\icons.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/icons/default/icons.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a311uwf1bl", "Integrity": "OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\icons\\default\\icons.min.js", "FileLength": 67093, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "VoZSG//lSaHOBHBXqzIbyVmanU8XWuqiBj04v6fMQ/I=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\de.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/langs/de#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wvtppe077w", "Integrity": "jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\de.js", "FileLength": 14915, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "WnEV/l5cneBCK0nsvQVEqncH39RPvN4SAJjrAmaQToE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\fr.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/langs/fr#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qch6z36znb", "Integrity": "WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\fr.js", "FileLength": 15498, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "9W6EcjCIlxadtjw14SSFdSSWFExy/rlG0Q2IyLOsfJ0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\it.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/langs/it#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xwf5nig97g", "Integrity": "0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\it.js", "FileLength": 14864, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "DZEbfrJFm52fwEQjOJXjCkE72HPragKUJiLc2itdyuw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\license.txt", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/license#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6q9vckcrvl", "Integrity": "AuFIf7NMpoTAxHiBJGeJ+fnLu3ZdewyqbfBPQmvu6Ss=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\license.txt", "FileLength": 1123, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "+whGHgfTldPe3soOT/RWjgbIHxdhSCqmAH6NZqCOEPc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\models\\dom\\model.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/models/dom/model.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "duy7y4d4ys", "Integrity": "JW4mmSPFbukV+MlXJt4j3+Ck9/YvSjsTIeWxKqhApYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\models\\dom\\model.min.js", "FileLength": 96635, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "jp0dPP2MqUkiPzjwxIpSb1+7vqMLxrvpLqE0qTmDZU4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\advlist\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/advlist/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8mf5u9u62l", "Integrity": "sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\advlist\\plugin.min.js", "FileLength": 3596, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "rF7EnaTL0Etda3k81WGQSvZVLuObKIeByfDQGR6dWCY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\anchor\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/anchor/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jgmmjjao6x", "Integrity": "M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\anchor\\plugin.min.js", "FileLength": 2519, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "DvxySGpbS3OG9tkab7WYRRL0o2xcg99ZA1Y85CyToVQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autolink\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/autolink/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5j7akqptjm", "Integrity": "X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB+EALh70nY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autolink\\plugin.min.js", "FileLength": 3262, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "+/lNBda8hmtJMgaxJnI5w8XB+SN0sNaLjwmPn2QpOc8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autoresize\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/autoresize/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "prxhfvklc1", "Integrity": "NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autoresize\\plugin.min.js", "FileLength": 2523, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "orjtjZxjP/uo9tbrd5Ob9vyxgznDa+u8MsfnC+YM4Cw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autosave\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/autosave/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cjafcovg7d", "Integrity": "GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autosave\\plugin.min.js", "FileLength": 3331, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "dfG6at8+hC6E4y2s4lygGJ6TAWRTJplu4b0ss9z9+H8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\charmap\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/charmap/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k7ldt4dufz", "Integrity": "YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\charmap\\plugin.min.js", "FileLength": 11014, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "NbaB1hrtz4FysH9Uaa69AZOcWlhkgixkCe2IAqI4VvI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\codesample\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/codesample/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c8susjty0k", "Integrity": "lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\codesample\\plugin.min.js", "FileLength": 48071, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Unn1FG0edxcLjipzNhwpojBIEiOmRUdJOdfZmHSXAro=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\code\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/code/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4ddczsiarp", "Integrity": "WJ7/J6y6iN/ejUeEuBKwRHYYr19C+dswBchCCc4i4dc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\code\\plugin.min.js", "FileLength": 880, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "6L1YMw5IOPNDDCXRds3z8xG0lpj0GDHOUAgmVgbfrBg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\directionality\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/directionality/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3a71kovr6i", "Integrity": "G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\directionality\\plugin.min.js", "FileLength": 4363, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "AYfMU+LYkZzpED52TIPgywa4pqYbTlB9/gdYfB5Ax0w=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "liga2smdmf", "Integrity": "HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.js", "FileLength": 410112, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "K3SeqrKQF8qhbjqy42NtgkC5OQoJ9rJ9mRpqHh1IY0U=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yu9bjgbclx", "Integrity": "VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.min.js", "FileLength": 416097, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2Q1XIo1omvH2y2NFtQ1Ks29H2WbWtewBZ9zV5nclrFc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zrqg1ly84r", "Integrity": "cd/Y1gLoV++Ss2+uK/pBed5ezvmZvvkKUbiNZlAfeNg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.js", "FileLength": 186921, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "55gTlAVQaJ6iXiV5jyBac9nQzzh/ZKH412JjCz6MHV8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uvtj9eiepa", "Integrity": "9qA+svinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.min.js", "FileLength": 192857, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "M79NN+6ypie1/Blg3WeFoTvo/fCzZg/D+a+7wBrCW10=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/emoticons/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tyqdbacaxt", "Integrity": "O6NJFEHcJ+C0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\plugin.min.js", "FileLength": 6393, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "my0tupT3+2hz9YX78fR4cSutZE2jiIY7VqYXLcZo9ao=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\fullscreen\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h0f14fag6o", "Integrity": "QcJ3NX+BjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\fullscreen\\plugin.min.js", "FileLength": 14724, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "IrVE25sJamlDmuF32SBk+/EaQEnZnYke7lZB+PPnrkw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\help\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/help/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "44sxdvkzh1", "Integrity": "bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\help\\plugin.min.js", "FileLength": 14146, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "VgFjm4G85aOgHzzOLjB3Kl8baONSRtpRjgznsAeFMn4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\image\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/image/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cblduhn0ke", "Integrity": "KN/12ahHVS1ShB0dbnggNsZbe05+a6TxiB9U8Z0Hcm4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\image\\plugin.min.js", "FileLength": 19345, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "iU8zMkyN8ovKh27DLK93Xo3ng0sBhK8AFyC6ZaQFUVU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\importcss\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/importcss/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47anb2m<PERSON>o", "Integrity": "YOYNs4IzRdIEB9dGXNeF4+xuMtua3xaUzIgtruEYDRY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\importcss\\plugin.min.js", "FileLength": 4056, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Bm+xxMvfc9W8QOppxdnVPcgTgCvKXpJv353Hd0dAnIY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\insertdatetime\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qv8sml9f80", "Integrity": "9rVcmFSBroQ24NjkfK+7PfynK30dkzpwA4j7PjXNLV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\insertdatetime\\plugin.min.js", "FileLength": 2886, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "tWd3QVmhUodmnzzBiIUvjUUNhwHnDJIlQ2PP7rnDtDw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\link\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/link/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l1bjazi1ht", "Integrity": "qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\link\\plugin.min.js", "FileLength": 15655, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "7p0t0+q+I4bRhKPedF4Y21/JNfBkydqw3gCcOQYjINA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\lists\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/lists/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pbb8yvsmll", "Integrity": "q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf+EkYTIw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\lists\\plugin.min.js", "FileLength": 24503, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ds+BlQpgcvOsxGl6JQV9D2u299hjgtLA1wCJxiNdKs8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\media\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/media/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7iyho1y9tr", "Integrity": "7+RnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\media\\plugin.min.js", "FileLength": 16698, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "x3ioJedF5+yLWPHkm9X03ssvmNt5mj9HQfjIJWqav+w=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\nonbreaking\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zlpcbzoevc", "Integrity": "WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\nonbreaking\\plugin.min.js", "FileLength": 1419, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "4GHUbDRxepDTmGkbPFWrIhi3/i8ENJNO2VoYXOc+60I=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\pagebreak\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ovwbmcvqah", "Integrity": "NHGv4s6mg2R/cI+Sc+llgOQTEuBxCFcWgA7XO4s5wQs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\pagebreak\\plugin.min.js", "FileLength": 1509, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "4v7VvCdycfJQDrBB11vTFfb1ByPlEd5n5Yo4SSIcKrc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\preview\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/preview/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6lkls1wqwx", "Integrity": "huRPOwMMtY+MMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\preview\\plugin.min.js", "FileLength": 1721, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "WifBz0Wx15NUGr/VkfHgugN2I8sQFsrqHs7tahJ3OhU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\quickbars\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/quickbars/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3qkdkjqh6m", "Integrity": "K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\quickbars\\plugin.min.js", "FileLength": 5071, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "JZ9vlMfVr2bi1geZX/PKk6kAWqfsBAuIcyir8YmlR4E=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\save\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/save/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "39ecmwg4fk", "Integrity": "2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\save\\plugin.min.js", "FileLength": 1595, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "dsT2a96HRtQbnj2r1fcfHt9o16vakrxAgXqNM8/o09M=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\searchreplace\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hd8f6h4flh", "Integrity": "230LS7ZgEYDlOs8uqnxzpIKgvz8+OM7f3HBwWyLBjR4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\searchreplace\\plugin.min.js", "FileLength": 13420, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Cs6FGzHjLf5uE7G5c/GxcmTNkTW/9BlPV3zjM4639tk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\table\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/table/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gt40yiyx87", "Integrity": "ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\table\\plugin.min.js", "FileLength": 47313, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "ZapQ30z1uKLIwp2EdyTTxACHYUtD24b12fqqxRF33Fg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\template\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/template/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3jv5k5vtg9", "Integrity": "IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\template\\plugin.min.js", "FileLength": 8262, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Ay7g9g4Fr3A0RSTHHhT+JLbeioRokgS1TJBCcrofIsg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\visualblocks\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gbx3kasexj", "Integrity": "bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\visualblocks\\plugin.min.js", "FileLength": 1233, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "aohd01bjesIfDJO0jG26AQpjI5O6YaW5kxBi7jDHiPU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\visualchars\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/visualchars/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4igg1krqg4", "Integrity": "jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm+HijgbZpw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\visualchars\\plugin.min.js", "FileLength": 5872, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "AYh4/0e9nEBM9ywvVyn3B4sb9LzfWcBDDi1Sffwui48=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\wordcount\\plugin.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/plugins/wordcount/plugin.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ra9jyp21g1", "Integrity": "03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\wordcount\\plugin.min.js", "FileLength": 11922, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "iBn0QCZX+bmaweBXuO4FzVmKFlh3G3gO2BY2utU0wOQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\dark\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/content/dark/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c0bbxs3g2a", "Integrity": "hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\dark\\content.min.css", "FileLength": 1217, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "T6taIsX8lA+qVI98kplGgwblE/b4vZKF/l2PrQeuvI8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\default\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/content/default/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dehv3jch66", "Integrity": "K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\default\\content.min.css", "FileLength": 1150, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "VVfqntOtYKrz0Hf2OcWVgILQquTHK3j2PAq14b2jAYo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\document\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/content/document/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qcb3yuv502", "Integrity": "OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m+Ixm4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\document\\content.min.css", "FileLength": 1249, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2rSw2IeQe5C+2IlUqqTpaRCF37dnJY1tdsFSCKW9yDw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5-dark\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o43ooccw0k", "Integrity": "2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5-dark\\content.min.css", "FileLength": 1220, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Mu9nRaZfVscMxTHBaoShBrVgsmYfmp9wM5ENYcBiB/E=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dehv3jch66", "Integrity": "K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5\\content.min.css", "FileLength": 1150, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Y2N8VM4vR5PBMdZ8hze5kxQvPeReAz+VzIRO7j+IBNw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\writer\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/content/writer/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2coynlc21", "Integrity": "HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\writer\\content.min.css", "FileLength": 1171, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "zFiAtuALEoC/x5VdYQGtKc2J5brGFEsT1tQ92IChsnM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.inline.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9a0ytxc496", "Integrity": "D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.inline.min.css", "FileLength": 23262, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "/5GYFS50VJ5ywQEcfusBQoWvLtRJsiij0p53qRCx23E=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "brznt3mq19", "Integrity": "xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.min.css", "FileLength": 22932, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "PMJ58LH5UbEKY3JtGmi7CeKUpVI54F2zaGiEhP6mR7g=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uukcty6ctu", "Integrity": "lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.min.css", "FileLength": 72948, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "S3ocHw8xKUtZ0bb+BKUMVNTrI1z09FViS+6m/MfesI4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.shadowdom.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oy4aiorfkf", "Integrity": "YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.shadowdom.min.css", "FileLength": 509, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "eYxM+FJagMgvdgp6moq0+8pWpzZ3s6kPiP8xh2a/2oU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.inline.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9a0ytxc496", "Integrity": "D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.inline.min.css", "FileLength": 23262, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "s/b6miXkAXlcA3kA65y24y2GXustfFimB5IHX0Yqm2Y=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "57uyr91r6a", "Integrity": "n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.min.css", "FileLength": 23321, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "B5+gXf11Dd0zyscpV4iXbBO3gFYaNIaqgsx9CliYFgg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eoxyrui399", "Integrity": "Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.min.css", "FileLength": 72986, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "p5GBfxENXSQFEum+Q7Fu8A1PBG/8sLaTK0Wjngna2ho=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.shadowdom.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oy4aiorfkf", "Integrity": "YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.shadowdom.min.css", "FileLength": 509, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "xZIGPFgtH9Vd3euBf6OjA42Lp+K2D+YRs6eTNXBvFaQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.inline.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9a0ytxc496", "Integrity": "D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.inline.min.css", "FileLength": 23262, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Cb8kS3MKnM8S7TTqLeeVaYQT/eE4LY9N9JjtY0iUmzw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "brznt3mq19", "Integrity": "xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.min.css", "FileLength": 22932, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "e86ICtXXf/Dk6IEXDMtUWVpWfpu61slQopxJoPb4RA0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hdz6qpdk9t", "Integrity": "a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.min.css", "FileLength": 75477, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "SqpiyZGotcGJvcXtJESBh+gFjH+ckwY60MJ/BgD0+zY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.shadowdom.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oy4aiorfkf", "Integrity": "YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.shadowdom.min.css", "FileLength": 509, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "mItXOPDIjqTP98kRbpD22Jkrn19TLDBQB/D6XpOBNFA=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.inline.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9a0ytxc496", "Integrity": "D6mSXtRZaKfTBatlB3+xTIvbM/xFj9oYMv7r/64dMEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.inline.min.css", "FileLength": 23262, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "U/aGsMOkbFri+LkjDo9HBLJ0gtwyC4ZGZG0R1m1at+4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "57uyr91r6a", "Integrity": "n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.min.css", "FileLength": 23321, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "D9vME2ZDwzlQEF74Y+mXmQLkuvGzXj7WkLRHqRqBBe4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "39r29kkvt5", "Integrity": "iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.min.css", "FileLength": 75630, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "oOnY8pVBe0UOP6zjlkMaUhr1fezP8GjDf5fsUJ6yXOM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.shadowdom.min.css", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oy4aiorfkf", "Integrity": "YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.shadowdom.min.css", "FileLength": 509, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "0nizXD2P8qC0t2vA9SfPe6VBm8t3BL118M3Zx5lA1jQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\themes\\silver\\theme.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/themes/silver/theme.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gm9r9yi2u9", "Integrity": "iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\themes\\silver\\theme.min.js", "FileLength": 397436, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "g6Ej5haRl1DpSWc2a/NL6nM1F292racVlf2sIlAD2CQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\tinymce.d.ts", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/tinymce.d#[.{fingerprint}]?.ts", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0uz5kw6u8m", "Integrity": "qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\tinymce.d.ts", "FileLength": 127423, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "z55U9ByXxcmu8hF3jX046qhvmCPBeuStbYdaB0zvtiM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\tinymce.min.js", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "lib/tinymce/js/tinymce/tinymce.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k214tg81kt", "Integrity": "lOTcZxqVz8iqrI9BFx+QBG4pK4KRYH0gFUcExOiM4UE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tinymce\\js\\tinymce\\tinymce.min.js", "FileLength": 415254, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "pc0ZxgzfjxyfVIEseRQSCUq6i7OJSFim3cFdNZskZKw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ActivateEmail.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ActivateEmail#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "udop0am2c7", "Integrity": "so98aLKqKnptmAhoPbgYBbmi+M9SFzNaRC9dKgaUOS4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ActivateEmail.html", "FileLength": 1657, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Mz8v8Czl63uUi6zZ01f/7tZ10qgpIAl81sOA6ARw8LY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\AdminMessgAfterRegister.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/AdminMessgAfterRegister#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lhamn20ixv", "Integrity": "mR2QAn+33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\AdminMessgAfterRegister.html", "FileLength": 2895, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "eaDX70NKWWU5+CNcpSeBc693PjBUjm8X36C5BiUmzM0=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\AGB.pdf", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/AGB#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j4m08jd11l", "Integrity": "A7Tumj1R+JdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\AGB.pdf", "FileLength": 87467, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "SUiT5m42Qp8gCbdjy3t/WR4gEnOhtLC93ec/+6Zx2sE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/OTP-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jtsyozonns", "Integrity": "Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\OTP-de.html", "FileLength": 3467, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "xgU2cA+xSk8uyEU/RtN8Uf4im6wGfWGqr8hMhX8saq4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/OTP-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t3if8ouq9v", "Integrity": "ly6d2QJbkWDhZc7PKFNcL1+tcMBEfTJUYJryuSgOy/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\OTP-en.html", "FileLength": 3439, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "IpVFcp/bQaAt6DH9z6/mVXL2wXWIZQwBSJJWPnUOaxg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/OTP-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "auiifrug0u", "Integrity": "HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\OTP-fr.html", "FileLength": 3458, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Cw3j7VQ7/pQl/3GpR73mJ6P0xjJHalk6uzmqntAAtAk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/OTP-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "teadc6g5tj", "Integrity": "C+bxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\OTP-it.html", "FileLength": 3452, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "nnGcBdOXhaulIp91kzRuqZdsaEjphWDtvWsoEidf4MI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\PaintingEmail.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/PaintingEmail#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "msrqokysyy", "Integrity": "yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\PaintingEmail.html", "FileLength": 9448, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "mC0Zk+cXg5DxipMAYcsF+yQBbEioyBWPTD/AEBG5YR8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendCleaning-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eah06epqyh", "Integrity": "etp/gX6noPqzc+tXcwmClbQo9WzkFQogW64nBjLc+og=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendCleaning-de.html", "FileLength": 8931, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "3PCVAtcw72SuMYAOlLDTd1lSzUQtYdJiXRus/xl8pCw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendCleaning-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w2sz3b30j5", "Integrity": "GGhA45b9Wn353uT5bAA05Sm/u1Dcovn+YlQaDcRauMg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendCleaning-en.html", "FileLength": 8689, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "nrNjAYOKq6HoHl7OdEd2MZ+6Iw2PnGseYRnixBZd5ME=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendCleaning-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mva6cvwvi2", "Integrity": "kqTHJJvLKhtwNcnDMLIqesff4XZnJTu8jrtqj2VcQso=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendCleaning-fr.html", "FileLength": 8765, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "u/mFDMERzOomi5AsViRGPLW2gG9JaiMMvemGu0mCGNk=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendCleaning-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3h3v2ga1ew", "Integrity": "V4VWxbz8LvRO7gzbBP8HNWV7Msx4V910pSIFOA9kKCw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendCleaning-it.html", "FileLength": 8890, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "4K/poZsEUfcWMsMZ8OWIIa8ayEVNiTyS0Fm0vJktFf4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMoving-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "njoyhsr5bf", "Integrity": "Pb50mGLUb8Tk8naZT8cSPET+yQFZt5Pb5y1f1BAvcc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMoving-de.html", "FileLength": 9098, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "k1INwiOYELvG2MhQlEGhTLfnj8dcD3nol8XiBHoVYag=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMoving-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "67i89gh55p", "Integrity": "IkfHI8K/23JNbFtU0/ZeA5lljcdcjDGxEvDMtZ/DolA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMoving-en.html", "FileLength": 8742, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "CkGmOIG1fPViHMlRJd0rB0mdn9NQCT6cbBC+uoWO/R4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMoving-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yckhrsfizq", "Integrity": "UtynHP+HvOhcaDW7AUOk9FR65BfQx+TIz+rCLbRTocI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMoving-fr.html", "FileLength": 9143, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "DN1isWFwGQvqiPTZVB/SUBCTqWq4njwmrWeoYhksnaU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMoving-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "10yhd1e1lz", "Integrity": "n0g5f6GOMY7bpxTxMFz/N87utPTUwJpTJT+tyTyVyMg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMoving-it.html", "FileLength": 9039, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "YJ+s05CZGVJbiCE0t5IfL0jqXg9kGXV9b6Q0SxkPpWg=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMovingAndCleaning-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "35202tt4v3", "Integrity": "KU8PDAdNeVn30JRyULL0u804oJzYrc8d6CX3fbuIO0w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMovingAndCleaning-de.html", "FileLength": 10443, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "FujKLWAeE9cm8j/Xzumw3W+uya1p7UKEsXINCQ9hn2M=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMovingAndCleaning-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "26crejesht", "Integrity": "pq8UT9f+1W7wPCim8rg/eq1vJDoJOXMrY/zbzBf5M+U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMovingAndCleaning-en.html", "FileLength": 10120, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "Xme5XfHO3AQFHQN8RzY98SO0yrcpaDMVfhoaTE0i1Ik=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMovingAndCleaning-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s7peg89fho", "Integrity": "2Ag4451CK9D/tbHSrN82Phm/VfON1oXA1xyJ3u4YNHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMovingAndCleaning-fr.html", "FileLength": 10196, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "m0mkRMEK6yX6Wyp5AHHSTj54uO0+4ciSDkS59jOwLrU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendMovingAndCleaning-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ljlwlkst8z", "Integrity": "78XyFrEg0CvgddjcZqtdQlQIME3qnvntyB4tSl3SJfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendMovingAndCleaning-it.html", "FileLength": 10154, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "PaDicRixDHy5y3/ByujYKGRkN+9jfhktKadNoLOwj/I=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendPainting-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iyhlunmbgw", "Integrity": "Kgh9Iv4xTTM47IiNy8D1VZLJT0emkWIAsjZ030jlC5k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendPainting-de.html", "FileLength": 6786, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "k0PuVWGYSWLlzOq5gLsoGJCGMHH9JQ9zw6ok+hj4XEI=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendPainting-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n2c8rnzm07", "Integrity": "H1bDI9iREwhfOoF0h8+LZhg9HSJIQCtYmGEk9E9TkiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendPainting-en.html", "FileLength": 6158, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "amrm1nhW9j3DIrstPP0BlN5GGNNRTonhfpWhJ8YiuvU=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendPainting-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrgowhsaqg", "Integrity": "HlH3oW9fXkOMatdvSVtn8Uiirp3296X2lgp8gNHjrYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendPainting-fr.html", "FileLength": 6213, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "iORd8ljE8nFoWAqVWLSwZMgJYNrr8uqfri9F9ET0vNQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendPainting-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8c6w9fv933", "Integrity": "GF3P5bzii5UZd1JXYVnblhIH3ocNjFUJcBDZ2gjOPOQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendPainting-it.html", "FileLength": 6740, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "pur5AZDM0dXEjL8Ih5ZrWGHvQF0qcLdg3UpBXUQdDk4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendWorkers-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iusv1d7r96", "Integrity": "ysj1WGDv/hIUOuaSFa/z8GE+KEnCNbmQHqD8nUUqrWw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendWorkers-de.html", "FileLength": 5613, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "t0/EK5J4MKFh5IJGLU3RgB+LDldoIlNqaUVYPo4aAcE=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendWorkers-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d86ygqvryp", "Integrity": "DbzgzHCzE+Jac0wrenLo3v7kLoabIaYgS8evpd/LzQM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendWorkers-en.html", "FileLength": 5104, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "QghJPUhDQVYrD3MO0YeTYkOYBNV0/pnYP4u/S878YPM=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendWorkers-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cpyp6zagkg", "Integrity": "ELKQvWRdLNCRMJ77fA7YRLcwZr0SKUzQaCwp3wFJafk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendWorkers-fr.html", "FileLength": 5143, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "q9O2MkWNRKT7MRyTDdxLeeT4J7ViSPIfnMlplVzq6EY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/RequestFrontendWorkers-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ti1ay39aoi", "Integrity": "NYQkDltgYgoh1Msy+aUHO5L+HY0sG0iCR/9cI17rHCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\RequestFrontendWorkers-it.html", "FileLength": 5103, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "hwARRz8yH317Y/bx7noYZbkzg7GE8tu8N1T4vdbel8s=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ResetPassword-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hdbkv46alh", "Integrity": "lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW+eaLXA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ResetPassword-de.html", "FileLength": 2596, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "RRtbShL+R9m0bOa+3rjapVUtIJ1BSnx0qDpJHf+IOjw=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ResetPassword-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bxwiwpvlxj", "Integrity": "L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ResetPassword-en.html", "FileLength": 2514, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2lKLD6fMqdE5zl1J5kU2Q2Kz9JD2E2wCqc+GKQPkEss=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ResetPassword-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pwodc3864l", "Integrity": "p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ResetPassword-fr.html", "FileLength": 2612, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "EjFFnz3LDwj9LWzKF8LfyQDXgNAhCAjF3PMqD3uv5BQ=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ResetPassword-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "601gadu4t9", "Integrity": "6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ResetPassword-it.html", "FileLength": 2565, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "dqnTr2LASC5MYnlNJF1b4IR9mVcuncBwsoBocFBC6j4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankY<PERSON>-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o58e1cibh7", "Integrity": "zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl+XD4bA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYou-de.html", "FileLength": 2337, "LastWriteTime": "2025-07-29T14:16:46+00:00"}, "YbtkD/xW7hGI9cijGgpYq1N+N2ciyIV2VntjZRmYIE8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYou-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hemmnn75si", "Integrity": "FwIJV5Ps3z2Kncz7jEnzjuvo+65nd+5g7k2/9BvgeaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYou-en.html", "FileLength": 2303, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "dylh5VLvunt206u8mpPX+gVOz4FMaDHwIZ+vj8gG9Gc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYou-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6nh9u6qvzv", "Integrity": "biio33qLgD+Oz3busPRlCyDjih1WDXjo+9caC57oM4o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYou-fr.html", "FileLength": 2373, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "2K0+QYrFY3chV6NtCzo8SOfZOdb81Uk8MCdugrvy6d4=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYou-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9q9oxzsxlo", "Integrity": "v4uLFordz1mainzhWH7i84VDaMaZ+yDtJnk8bHC60jY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYou-it.html", "FileLength": 2381, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "YIJuC1xUWWAwLh6eXK2doiQY4uE4xnPPlXQaXdqP/gY=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-de.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYouCustomer-de#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nt4xq5ndu1", "Integrity": "q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYouCustomer-de.html", "FileLength": 2605, "LastWriteTime": "2025-07-29T14:16:06+00:00"}, "Du09kUTtakBgTi5xlrV33WznplyN8gZSJrdZdRST5cc=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-en.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYouCustomer-en#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nssyq9ewpc", "Integrity": "uTt5oQGoIqqT6Is4ATphkPEdcRIIf+M0Tz+/F4gUUeM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYouCustomer-en.html", "FileLength": 2710, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "VrWvaRWtxflavlbMLLBGjI2pck93OiS12g/DgxBNaZ8=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-fr.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYouCustomer-fr#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sa0j1gvmik", "Integrity": "vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYouCustomer-fr.html", "FileLength": 2822, "LastWriteTime": "2025-07-27T06:47:46+00:00"}, "/nZdpiGnCPfGCYdOzYEvnqjAz++TV6WjceLUaHwVdoo=": {"Identity": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-it.html", "SourceId": "TaskDotNet", "SourceType": "Discovered", "ContentRoot": "E:\\TaskDotNet_27_07_2025\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\", "BasePath": "_content/TaskDotNet", "RelativePath": "Templates/ThankYouCustomer-it#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4sfjp0ek4d", "Integrity": "XJA5i0qj7q+sC+9uQYm5i3v0EOFHIeLQx+TxYD+VKiw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Templates\\ThankYouCustomer-it.html", "FileLength": 2749, "LastWriteTime": "2025-07-27T06:47:46+00:00"}}, "CachedCopyCandidates": {}}