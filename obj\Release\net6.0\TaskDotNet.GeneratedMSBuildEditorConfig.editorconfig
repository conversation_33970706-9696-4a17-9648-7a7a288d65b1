is_global = true
build_property.TargetFramework = net6.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = TaskDotNet
build_property.RootNamespace = TaskDotNet
build_property.ProjectDir = C:\Users\<USER>\Desktop\Aktuelle Projekte\TaskNet\Projekt\TaskDotNet_27_07_2025\TaskDotNet\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 6.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Desktop\Aktuelle Projekte\TaskNet\Projekt\TaskDotNet_27_07_2025\TaskDotNet
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 6.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/AccessDenied.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxBY2Nlc3NEZW5pZWQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/ConfirmEmail.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxDb25maXJtRW1haWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/ForgotPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxGb3Jnb3RQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/ForgotPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxGb3Jnb3RQYXNzd29yZENvbmZpcm1hdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/Lockout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxMb2Nrb3V0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/Logout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxMb2dvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/Manage/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxNYW5hZ2VcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/Manage/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxNYW5hZ2VcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxSZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/RegisterConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxSZWdpc3RlckNvbmZpcm1hdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/ResetPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxSZXNldFBhc3N3b3JkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/ResetPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxSZXNldFBhc3N3b3JkQ29uZmlybWF0aW9uLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/SendEmailConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxTZW5kRW1haWxDb25maXJtYXRpb24uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Account/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWNjb3VudFxfVmlld0ltcG9ydHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXJyb3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZhbGlkYXRpb25TY3JpcHRzUGFydGlhbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Error/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRXJyb3JcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Partner/Activities.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGFydG5lclxBY3Rpdml0aWVzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Partner/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGFydG5lclxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Partner/RechargeCredit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGFydG5lclxSZWNoYXJnZUNyZWRpdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Partner/Statistics.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGFydG5lclxTdGF0aXN0aWNzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Partner/TermsAndConditions.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGFydG5lclxUZXJtc0FuZENvbmRpdGlvbnMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/CleaningActivityDetails.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXENsZWFuaW5nQWN0aXZpdHlEZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/EmailToCustomer.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXEVtYWlsVG9DdXN0b21lci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/MovingActivityDetails.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXE1vdmluZ0FjdGl2aXR5RGV0YWlscy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/MovingAndCleaningActivityDetails.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXE1vdmluZ0FuZENsZWFuaW5nQWN0aXZpdHlEZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/PaintingAndGisperActivityDetails.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXFBhaW50aW5nQW5kR2lzcGVyQWN0aXZpdHlEZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/PurchasedActivites/WorkersActivityDetails.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUHVyY2hhc2VkQWN0aXZpdGVzXFdvcmtlcnNBY3Rpdml0eURldGFpbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Shared/Components/CompanyName/Default.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXENvbXBvbmVudHNcQ29tcGFueU5hbWVcRGVmYXVsdC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Shared/_LoginLayout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Mb2dpbkxheW91dC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Shared/_Notification.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Ob3RpZmljYXRpb24uY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Shared/_StatusMessage.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9TdGF0dXNNZXNzYWdlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Desktop/Aktuelle Projekte/TaskNet/Projekt/TaskDotNet_27_07_2025/TaskDotNet/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-agvfp3lfxp
