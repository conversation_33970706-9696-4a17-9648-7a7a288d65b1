﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/css/site.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nDHJdDTU6Iz19np37pHzXUkRFktd\u002BXe1TIcSYBSEXng="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1723"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022nDHJdDTU6Iz19np37pHzXUkRFktd\u002BXe1TIcSYBSEXng=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/css/site.kc2urnfzw5.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\css\site.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"kc2urnfzw5"},{"Name":"integrity","Value":"sha256-nDHJdDTU6Iz19np37pHzXUkRFktd\u002BXe1TIcSYBSEXng="},{"Name":"label","Value":"_content/TaskDotNet/css/site.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1723"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022nDHJdDTU6Iz19np37pHzXUkRFktd\u002BXe1TIcSYBSEXng=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/css/demo.9ok5r7wnkv.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\css\demo.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9ok5r7wnkv"},{"Name":"integrity","Value":"sha256-6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/css/demo.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2514"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00226S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/css/demo.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\css\demo.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2514"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00226S6cMlFb0Z4msPVyV91LxWzhXanxx4qXv0iJ7C2HECY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/ActivateEmail.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\ActivateEmail.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lON53pF5QfXQPeynlyNLW20fO1Jm3CHH\u002BrwkWshmpjY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"45815"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lON53pF5QfXQPeynlyNLW20fO1Jm3CHH\u002BrwkWshmpjY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/ActivateEmail.wf7zkmmsg2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\ActivateEmail.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wf7zkmmsg2"},{"Name":"integrity","Value":"sha256-lON53pF5QfXQPeynlyNLW20fO1Jm3CHH\u002BrwkWshmpjY="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/ActivateEmail.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"45815"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lON53pF5QfXQPeynlyNLW20fO1Jm3CHH\u002BrwkWshmpjY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/AGB.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\AGB.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"73761"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/AGB.q7mriwkq8p.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\AGB.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q7mriwkq8p"},{"Name":"integrity","Value":"sha256-NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/AGB.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"73761"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NjshtF3LNYoC4XF6kphSo786E52LjxwO/hPMhHc4nJk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Attention-removebg-preview.0eoyn9ev74.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Attention-removebg-preview.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0eoyn9ev74"},{"Name":"integrity","Value":"sha256-\u002BIo5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Attention-removebg-preview.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"17077"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022\u002BIo5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Attention-removebg-preview.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Attention-removebg-preview.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BIo5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"17077"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022\u002BIo5SSy0J/Yr5h8QNITI8ijnOPvZeBRSUn2Kpq2jKOg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/1.5dw95i7i20.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5dw95i7i20"},{"Name":"integrity","Value":"sha256-GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/avatars/1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14015"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"14015"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022GakibeacIikL1hB1FheaFS4jzVDwrlSuxEt7/2BM/58=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"20488"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/5.y7wk20hnyd.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y7wk20hnyd"},{"Name":"integrity","Value":"sha256-HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/avatars/5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20488"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022HvLf/joyX29gx1iOJmVCH48YYR9PJky53y7HIrgMoZg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/6.laj0069a7v.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"laj0069a7v"},{"Name":"integrity","Value":"sha256-U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/avatars/6.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15198"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\6.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"15198"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022U7ZWHRdqadq3R4Pdjgmy8IfOW0ph9f71C/aTXSwrUTQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/7.jzhuf0rlpo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jzhuf0rlpo"},{"Name":"integrity","Value":"sha256-ceq\u002BEmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/avatars/7.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15180"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ceq\u002BEmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/avatars/7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\avatars\7.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ceq\u002BEmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"15180"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ceq\u002BEmgetHDkW1J4Nxhn3STmQTcJAMU5qVoeY7CFo68=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/backgrounds/18.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\backgrounds\18.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/fVYHI8QJL\u002Baw0jk/AjqF8Q8usxEunzAwNftu49AN/o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"88783"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/fVYHI8QJL\u002Baw0jk/AjqF8Q8usxEunzAwNftu49AN/o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/backgrounds/18.zcrq1vg6ym.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\backgrounds\18.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zcrq1vg6ym"},{"Name":"integrity","Value":"sha256-/fVYHI8QJL\u002Baw0jk/AjqF8Q8usxEunzAwNftu49AN/o="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/backgrounds/18.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"88783"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/fVYHI8QJL\u002Baw0jk/AjqF8Q8usxEunzAwNftu49AN/o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/cleaning.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\cleaning.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7r9n/l0ePgee2rx7jPgsDkH7\u002BzeuOLGVqlySnPuze/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1739"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00227r9n/l0ePgee2rx7jPgsDkH7\u002BzeuOLGVqlySnPuze/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/cleaning.q6iwg7513g.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\cleaning.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q6iwg7513g"},{"Name":"integrity","Value":"sha256-7r9n/l0ePgee2rx7jPgsDkH7\u002BzeuOLGVqlySnPuze/w="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/cleaning.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1739"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00227r9n/l0ePgee2rx7jPgsDkH7\u002BzeuOLGVqlySnPuze/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/color.66as5qo0c3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\color.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"66as5qo0c3"},{"Name":"integrity","Value":"sha256-fm\u002BqDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/color.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1203"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022fm\u002BqDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/color.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\color.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fm\u002BqDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1203"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022fm\u002BqDDuQ4MB4tpAFy89RQaaqpuRvRqYn6hkWRs1YqR8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard1.6735s5w30p.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6735s5w30p"},{"Name":"integrity","Value":"sha256-jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Dashboard1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"152669"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"152669"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022jtDYwDrcKCKFzplnq9YZGY013YoPvQrpWxEd1bQFdvQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard2.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard2.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"51207"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard2.oq5d0n04gc.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard2.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oq5d0n04gc"},{"Name":"integrity","Value":"sha256-UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Dashboard2.jpeg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51207"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022UBr3r31nADQgJNCFkoK3xO3U8VLGejZTgd52aJ5qLDg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard_01.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard_01.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nEBoPob46ocCh8FvwhNE9aCwR\u002BZIDds0r\u002BWtOJJISvo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5418"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nEBoPob46ocCh8FvwhNE9aCwR\u002BZIDds0r\u002BWtOJJISvo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard_01.wf2p4xcoui.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard_01.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wf2p4xcoui"},{"Name":"integrity","Value":"sha256-nEBoPob46ocCh8FvwhNE9aCwR\u002BZIDds0r\u002BWtOJJISvo="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Dashboard_01.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5418"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nEBoPob46ocCh8FvwhNE9aCwR\u002BZIDds0r\u002BWtOJJISvo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard_02.bgu0r6dzip.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard_02.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bgu0r6dzip"},{"Name":"integrity","Value":"sha256-K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Dashboard_02.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1989"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard_02.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard_02.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1989"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022K/2Qmr07EaNFTO7GqDYwLxbvOVcs/W4XSQClUHjeLs8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard_03.dr26h7x9hl.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard_03.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dr26h7x9hl"},{"Name":"integrity","Value":"sha256-edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Dashboard_03.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3566"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Dashboard_03.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Dashboard_03.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3566"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022edBYXJ8iR9Z/u46Jwx1DjKInYvErDCdDqz4yBC5YhD8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/de-flag.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\de-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-szXHI\u002Bv9CKe9gq/sSk7wCT3Ff\u002BxiY9WkgU9T3GkN/Co="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"20056"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022szXHI\u002Bv9CKe9gq/sSk7wCT3Ff\u002BxiY9WkgU9T3GkN/Co=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/de-flag.xjszvk8663.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\de-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xjszvk8663"},{"Name":"integrity","Value":"sha256-szXHI\u002Bv9CKe9gq/sSk7wCT3Ff\u002BxiY9WkgU9T3GkN/Co="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/de-flag.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20056"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022szXHI\u002Bv9CKe9gq/sSk7wCT3Ff\u002BxiY9WkgU9T3GkN/Co=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/1.1dcgywla5o.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1dcgywla5o"},{"Name":"integrity","Value":"sha256-a6t4IujkLq\u002B637JfyvpU/86ABXnFoB8MdTUNkb6kgRI="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"18135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022a6t4IujkLq\u002B637JfyvpU/86ABXnFoB8MdTUNkb6kgRI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a6t4IujkLq\u002B637JfyvpU/86ABXnFoB8MdTUNkb6kgRI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"18135"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022a6t4IujkLq\u002B637JfyvpU/86ABXnFoB8MdTUNkb6kgRI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/11.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\11.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"19087"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/11.uj5sfxbjnm.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\11.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uj5sfxbjnm"},{"Name":"integrity","Value":"sha256-5vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/11.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19087"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00225vGI0LfpSt/t7R3BRN5cbvkiIndtuQc2P26VTmhBCjc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/12.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\12.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"28075"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/12.xqqhdiskkx.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\12.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xqqhdiskkx"},{"Name":"integrity","Value":"sha256-qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/12.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"28075"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022qRb0YTolNxhZcBKxjilnX5nPu5QGChNoOdFZiNDx9pA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/13.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\13.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d\u002BsCErs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"12929"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d\u002BsCErs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/13.sqtpmqt4ko.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\13.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sqtpmqt4ko"},{"Name":"integrity","Value":"sha256-LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d\u002BsCErs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/13.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"12929"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022LE9DuCIGutXSCGXben/ETqROLKWFYowfILx/d\u002BsCErs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/17.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\17.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"19716"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00220rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/17.mbfplb2995.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\17.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mbfplb2995"},{"Name":"integrity","Value":"sha256-0rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/17.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19716"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00220rrsaoqTIj8PlnoHjT2iT93sfPLwJt9QQLN4CYmiwYo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/18.4kfik9hhpd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\18.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4kfik9hhpd"},{"Name":"integrity","Value":"sha256-RMeMm27YNLobckPL3MTiDAEeuUKVF\u002B/AY4KPuNp\u002BcwU="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/18.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"31223"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022RMeMm27YNLobckPL3MTiDAEeuUKVF\u002B/AY4KPuNp\u002BcwU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/18.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\18.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RMeMm27YNLobckPL3MTiDAEeuUKVF\u002B/AY4KPuNp\u002BcwU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"31223"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022RMeMm27YNLobckPL3MTiDAEeuUKVF\u002B/AY4KPuNp\u002BcwU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/19.a3beqhyfaj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\19.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a3beqhyfaj"},{"Name":"integrity","Value":"sha256-WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/19.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/19.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\19.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"15553"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022WrLKEezeub2INc0IH/3ZSISrGotKXmApabXkuOlGUHw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/2.j3cw3e50ho.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j3cw3e50ho"},{"Name":"integrity","Value":"sha256-wc\u002B6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"13332"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wc\u002B6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wc\u002B6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13332"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wc\u002B6ap17bBXk7icsl441cpNtf70chVoSoXrXRHqu/jo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/20.6ffy4hs6lt.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\20.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6ffy4hs6lt"},{"Name":"integrity","Value":"sha256-kipYcAx\u002BdLYKPwZ98vbg\u002B\u002BWhjJgPD9C\u002BaVL0/wOWyXI="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/20.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16593"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022kipYcAx\u002BdLYKPwZ98vbg\u002B\u002BWhjJgPD9C\u002BaVL0/wOWyXI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/20.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\20.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kipYcAx\u002BdLYKPwZ98vbg\u002B\u002BWhjJgPD9C\u002BaVL0/wOWyXI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16593"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022kipYcAx\u002BdLYKPwZ98vbg\u002B\u002BWhjJgPD9C\u002BaVL0/wOWyXI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"24285"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/3.rga7lkqrdc.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rga7lkqrdc"},{"Name":"integrity","Value":"sha256-u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24285"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022u5X5mp03DIEAhT9BNpA8dRldGSRpNg/DKCgmtuN7fSA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-h6CzEKD4MIeFaGkx\u002Bb0QuMkWMTa\u002BYkPPY7/hdZKSjG0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"22875"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022h6CzEKD4MIeFaGkx\u002Bb0QuMkWMTa\u002BYkPPY7/hdZKSjG0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/4.tv0gns518i.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tv0gns518i"},{"Name":"integrity","Value":"sha256-h6CzEKD4MIeFaGkx\u002Bb0QuMkWMTa\u002BYkPPY7/hdZKSjG0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22875"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022h6CzEKD4MIeFaGkx\u002Bb0QuMkWMTa\u002BYkPPY7/hdZKSjG0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IePvmLa1hS\u002BrQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"22938"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022IePvmLa1hS\u002BrQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/5.vk7ev4b9zi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\5.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vk7ev4b9zi"},{"Name":"integrity","Value":"sha256-IePvmLa1hS\u002BrQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/5.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22938"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022IePvmLa1hS\u002BrQhdTHvyz2DDS8FVrxQ9W28QJ4S0lIwA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/7.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\7.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/fRu\u002BiZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"21183"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/fRu\u002BiZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/elements/7.n38sfx2p3i.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\elements\7.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n38sfx2p3i"},{"Name":"integrity","Value":"sha256-/fRu\u002BiZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/elements/7.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"21183"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/fRu\u002BiZq/ZKlxul5HBvvGwMnXsHOKSKQ7FF0ftE37Wg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/favicon/favicon.2hcxmlxs2r.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\favicon\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2hcxmlxs2r"},{"Name":"integrity","Value":"sha256-ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/favicon/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1393"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/favicon/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\favicon\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1393"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022ZhdZMzzh105upKWgioimw/aKt56k3eqSVgY26ka5mSM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Foto_Register_Form.aoece3ix9v.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Foto_Register_Form.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"aoece3ix9v"},{"Name":"integrity","Value":"sha256-thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Foto_Register_Form.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"90383"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Foto_Register_Form.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Foto_Register_Form.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"90383"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022thCGppWhwHuk0AC0E9YOmgxOjF7rmfmQEoiYQc8kvlw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/fr-flag.l2zu6d6aky.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\fr-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l2zu6d6aky"},{"Name":"integrity","Value":"sha256-DQdfguK09Pp2IQGulUiKQl0t\u002BfQMW34SCVRQnGNYeb8="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/fr-flag.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16395"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022DQdfguK09Pp2IQGulUiKQl0t\u002BfQMW34SCVRQnGNYeb8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/fr-flag.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\fr-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DQdfguK09Pp2IQGulUiKQl0t\u002BfQMW34SCVRQnGNYeb8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"16395"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022DQdfguK09Pp2IQGulUiKQl0t\u002BfQMW34SCVRQnGNYeb8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Gekauft.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Gekauft.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"8670"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Gekauft.rlsnhgmo07.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Gekauft.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rlsnhgmo07"},{"Name":"integrity","Value":"sha256-j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Gekauft.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8670"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022j0FMWEtWiYk/iRWvi7hpMBQJbQ8SSNiqPpgeECSHnBU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/gisper.c7xywxqxv1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\gisper.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c7xywxqxv1"},{"Name":"integrity","Value":"sha256-mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/gisper.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"808"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/gisper.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\gisper.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"808"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mACdmOyWbfVfaOT7snBVUQudHFS9dFQCDOwkEha0dKQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/iconfacebook.dtpc2iklyd.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\iconfacebook.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dtpc2iklyd"},{"Name":"integrity","Value":"sha256-sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/iconfacebook.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1316"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/iconfacebook.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\iconfacebook.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1316"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022sVeKg9pECr80NtWARqhMXCHDeAyjIcsvQA7rxIg2vfc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/iconinsta.g2myl4c9mh.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\iconinsta.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g2myl4c9mh"},{"Name":"integrity","Value":"sha256-OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/iconinsta.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3489"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/iconinsta.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\iconinsta.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3489"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022OGns6sYmgaRTAhNaahcbjd0J77De5QfwTOXBBAvTyAc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/iconlinked.64c1t2dskq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\iconlinked.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"64c1t2dskq"},{"Name":"integrity","Value":"sha256-lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/iconlinked.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"608"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/iconlinked.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\iconlinked.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"608"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lki8XsRUb1INt7yaPDLa4rU4TZO9VVRmOkspedGnl0E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/asana.m8ed7lopx2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\asana.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"m8ed7lopx2"},{"Name":"integrity","Value":"sha256-qtt8Djtym3fWp5n\u002BPpH9HVsMcfniOIs8KlSEVvgMFcs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/asana.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2236"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qtt8Djtym3fWp5n\u002BPpH9HVsMcfniOIs8KlSEVvgMFcs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/asana.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\asana.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qtt8Djtym3fWp5n\u002BPpH9HVsMcfniOIs8KlSEVvgMFcs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2236"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qtt8Djtym3fWp5n\u002BPpH9HVsMcfniOIs8KlSEVvgMFcs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/behance.coaxahx6t3.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\behance.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"coaxahx6t3"},{"Name":"integrity","Value":"sha256-tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw\u002B5ck="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/behance.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1731"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw\u002B5ck=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/behance.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\behance.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw\u002B5ck="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1731"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022tI1zFLsfbdaZZ0d8qXIHD26raxUCvNRQIvc/gNw\u002B5ck=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/dribbble.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\dribbble.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2848"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/dribbble.w706m358zy.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\dribbble.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w706m358zy"},{"Name":"integrity","Value":"sha256-nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/dribbble.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2848"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nFjwdkk45306nydk17zF40vBBVlUD7in5gK7bzesB7c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/facebook.h6xi8zk54u.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\facebook.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h6xi8zk54u"},{"Name":"integrity","Value":"sha256-h5NQXedd04yWx\u002BAtNmndoyZpe9mF71FgFrcv2/XGHIk="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/facebook.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"681"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022h5NQXedd04yWx\u002BAtNmndoyZpe9mF71FgFrcv2/XGHIk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/facebook.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\facebook.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-h5NQXedd04yWx\u002BAtNmndoyZpe9mF71FgFrcv2/XGHIk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"681"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022h5NQXedd04yWx\u002BAtNmndoyZpe9mF71FgFrcv2/XGHIk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/github.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\github.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2169"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/github.q7i6lubamj.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\github.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q7i6lubamj"},{"Name":"integrity","Value":"sha256-SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/github.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2169"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SsEbqlIero6v0KTzFtKULqgTbbHlf2fopiVU3rNo/eQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/google.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\google.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1932"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/google.yn247w80id.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\google.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yn247w80id"},{"Name":"integrity","Value":"sha256-fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/google.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1932"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022fk0pyxCqMVZMWsLcBvKoiymP8ge6euW1y0hLwnjzXhw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/instagram.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\instagram.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR\u002Bbg/6Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3128"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR\u002Bbg/6Q=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/instagram.t9b1hn39ce.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\instagram.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t9b1hn39ce"},{"Name":"integrity","Value":"sha256-rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR\u002Bbg/6Q="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/instagram.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3128"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022rzPFpJpaFX/c33A9uelKUw7/WreRQb6FwDQYR\u002Bbg/6Q=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/mailchimp.37ik0yzl65.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\mailchimp.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"37ik0yzl65"},{"Name":"integrity","Value":"sha256-H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s\u002BggWOtxrWNfzK4="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/mailchimp.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1405"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s\u002BggWOtxrWNfzK4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/mailchimp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\mailchimp.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s\u002BggWOtxrWNfzK4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1405"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022H9EcNXJ47xJSU4UZYTpkdBFBcAQ0s\u002BggWOtxrWNfzK4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/slack.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\slack.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oE0mYTJAJstA8DXOzLlAUnWFXyWFtU\u002BLhHcbRcjs0mM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2550"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oE0mYTJAJstA8DXOzLlAUnWFXyWFtU\u002BLhHcbRcjs0mM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/slack.w43jfux8yp.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\slack.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w43jfux8yp"},{"Name":"integrity","Value":"sha256-oE0mYTJAJstA8DXOzLlAUnWFXyWFtU\u002BLhHcbRcjs0mM="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/slack.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2550"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oE0mYTJAJstA8DXOzLlAUnWFXyWFtU\u002BLhHcbRcjs0mM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/twitter.btqwmt39wx.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\twitter.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"btqwmt39wx"},{"Name":"integrity","Value":"sha256-vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/brands/twitter.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1564"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/brands/twitter.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\brands\twitter.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1564"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022vymBSGM14nM2pEAl01p8GKLm8OifsEwP3CPlsZTdCfI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-primary.7cz3d43tgn.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\cc-primary.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7cz3d43tgn"},{"Name":"integrity","Value":"sha256-pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9\u002B0juV\u002BSQ0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-primary.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"702"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9\u002B0juV\u002BSQ0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-primary.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\cc-primary.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9\u002B0juV\u002BSQ0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"702"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022pwKhaz9nifgYMTnW965kJL3jWm/1EKMaa9\u002B0juV\u002BSQ0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-success.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\cc-success.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rC5AxYDuZkEY6\u002BUekvJZ4HJdxe48g5byrHETLimP4Lc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"776"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022rC5AxYDuZkEY6\u002BUekvJZ4HJdxe48g5byrHETLimP4Lc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-success.sozk5ui7zb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\cc-success.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sozk5ui7zb"},{"Name":"integrity","Value":"sha256-rC5AxYDuZkEY6\u002BUekvJZ4HJdxe48g5byrHETLimP4Lc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-success.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"776"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022rC5AxYDuZkEY6\u002BUekvJZ4HJdxe48g5byrHETLimP4Lc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-warning.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\cc-warning.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"689"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00225uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-warning.yqdeq55wew.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\cc-warning.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yqdeq55wew"},{"Name":"integrity","Value":"sha256-5uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/cc-warning.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"689"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00225uSksHD1iBGBuMKXXgpOIOvLhnKzUAGCp2pLj4cCnOg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/chart-success.9zqdfavt9a.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\chart-success.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9zqdfavt9a"},{"Name":"integrity","Value":"sha256-1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/chart-success.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1528"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00221dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/chart-success.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\chart-success.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-1dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1528"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00221dA1GFlgp4AwyiK2EtGiZitxoN4Up5nVjVMxdn/Fxfk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/chart.j55jf7gsvq.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\chart.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j55jf7gsvq"},{"Name":"integrity","Value":"sha256-KXQATGROWvL9P\u002ByaqtuEY7vfQo711XTl3mY\u002BX1UTsYo="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/chart.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1491"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KXQATGROWvL9P\u002ByaqtuEY7vfQo711XTl3mY\u002BX1UTsYo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/chart.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\chart.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KXQATGROWvL9P\u002ByaqtuEY7vfQo711XTl3mY\u002BX1UTsYo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1491"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KXQATGROWvL9P\u002ByaqtuEY7vfQo711XTl3mY\u002BX1UTsYo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/paypal.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\paypal.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MmXG7F0O5\u002BY2\u002BeC/my8aBVs83MestyL6XZQHgb/yzvI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1090"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MmXG7F0O5\u002BY2\u002BeC/my8aBVs83MestyL6XZQHgb/yzvI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/paypal.yw16yx1qqs.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\paypal.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yw16yx1qqs"},{"Name":"integrity","Value":"sha256-MmXG7F0O5\u002BY2\u002BeC/my8aBVs83MestyL6XZQHgb/yzvI="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/paypal.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1090"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MmXG7F0O5\u002BY2\u002BeC/my8aBVs83MestyL6XZQHgb/yzvI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/wallet-info.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\wallet-info.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"936"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/wallet-info.uyaw46hsrb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\wallet-info.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uyaw46hsrb"},{"Name":"integrity","Value":"sha256-hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/wallet-info.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"936"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022hmsDCiMg2IYZ46fBHTBAql6505f9g0bUtvRwdnU3lGk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/wallet.eik0yqeyxb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\wallet.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eik0yqeyxb"},{"Name":"integrity","Value":"sha256-Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ\u002BuhFMjuc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icons/unicons/wallet.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"920"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ\u002BuhFMjuc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icons/unicons/wallet.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icons\unicons\wallet.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ\u002BuhFMjuc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"920"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Ki7fmsT7OVyKlrdyWPF5ao36IBgIsjPmTUQ\u002BuhFMjuc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icontwitter.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icontwitter.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9\u002BwfmmWHE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1834"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9\u002BwfmmWHE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/icontwitter.stbbn09hjr.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\icontwitter.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"stbbn09hjr"},{"Name":"integrity","Value":"sha256-oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9\u002BwfmmWHE="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/icontwitter.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1834"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oFeO3c4DUPYd2M0TQzorohaZv0paDHMg5w9\u002BwfmmWHE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/girl-doing-yoga-light.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\girl-doing-yoga-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"219096"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/girl-doing-yoga-light.pqilfow7ks.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\girl-doing-yoga-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pqilfow7ks"},{"Name":"integrity","Value":"sha256-IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/illustrations/girl-doing-yoga-light.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"219096"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IeHIs4cgSDEbvvHmQ0Gw4pMHvi4ux5eIJE9JZ4nOrOU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/girl-with-laptop-light.2cvzwvwj7q.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\girl-with-laptop-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2cvzwvwj7q"},{"Name":"integrity","Value":"sha256-nrpYnyWa0DzjPEfjd\u002BRkI9dyXy9Holiw4FjsD7dGLc0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/illustrations/girl-with-laptop-light.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"183173"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nrpYnyWa0DzjPEfjd\u002BRkI9dyXy9Holiw4FjsD7dGLc0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/girl-with-laptop-light.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\girl-with-laptop-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-nrpYnyWa0DzjPEfjd\u002BRkI9dyXy9Holiw4FjsD7dGLc0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"183173"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022nrpYnyWa0DzjPEfjd\u002BRkI9dyXy9Holiw4FjsD7dGLc0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/in-prograss.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\in-prograss.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk\u002BaKWCngXgs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"173307"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk\u002BaKWCngXgs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/in-prograss.u5yfcb6n2t.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\in-prograss.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u5yfcb6n2t"},{"Name":"integrity","Value":"sha256-Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk\u002BaKWCngXgs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/illustrations/in-prograss.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"173307"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Tu1DFpVbrkuJP9LdkBgP9PPU/GkSrUnbk\u002BaKWCngXgs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/man-with-laptop-light.lbgi9tpxrg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\man-with-laptop-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lbgi9tpxrg"},{"Name":"integrity","Value":"sha256-ITDy9whlrMB5qxY945SKc\u002BtYgjQxgFndh3dqPZzC8V0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/illustrations/man-with-laptop-light.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8826"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ITDy9whlrMB5qxY945SKc\u002BtYgjQxgFndh3dqPZzC8V0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/man-with-laptop-light.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\man-with-laptop-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ITDy9whlrMB5qxY945SKc\u002BtYgjQxgFndh3dqPZzC8V0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"8826"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ITDy9whlrMB5qxY945SKc\u002BtYgjQxgFndh3dqPZzC8V0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/page-misc-error-light.4yk20wtg5w.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\page-misc-error-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4yk20wtg5w"},{"Name":"integrity","Value":"sha256-dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/illustrations/page-misc-error-light.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"139086"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/page-misc-error-light.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\page-misc-error-light.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"139086"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022dNmrwUBStbi15vXz0sKEf3NKeZGzrNuH0z5C0q7KeLw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/page-misc-error-light2.hu89au1p7j.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\page-misc-error-light2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hu89au1p7j"},{"Name":"integrity","Value":"sha256-CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/illustrations/page-misc-error-light2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"216684"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/illustrations/page-misc-error-light2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\illustrations\page-misc-error-light2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"216684"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CWf8MTkHsvJlZ3hLYNI0PXzOeDweP2ZTv49aGlJaIhk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/it-flag.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\it-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13908"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/it-flag.qssp6wqowt.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\it-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qssp6wqowt"},{"Name":"integrity","Value":"sha256-CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/it-flag.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"13908"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022CkRSn/BfPWJHtk62uU1VLGlTgBcBuJm9I1JdLJvaR2g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/language.j2dl353fui.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\language.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j2dl353fui"},{"Name":"integrity","Value":"sha256-lcNl3e\u002BJH\u002BC5Ey3\u002BBNS9kbCRVmYHFtRCqf/cje/\u002BZpQ="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/language.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9250"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lcNl3e\u002BJH\u002BC5Ey3\u002BBNS9kbCRVmYHFtRCqf/cje/\u002BZpQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/language.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\language.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lcNl3e\u002BJH\u002BC5Ey3\u002BBNS9kbCRVmYHFtRCqf/cje/\u002BZpQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9250"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022lcNl3e\u002BJH\u002BC5Ey3\u002BBNS9kbCRVmYHFtRCqf/cje/\u002BZpQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/lock.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\lock.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"8410"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/lock.qdws6rar45.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\lock.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qdws6rar45"},{"Name":"integrity","Value":"sha256-EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/lock.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8410"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022EvuiWDbTArvfCwsh09ebJZCI38uYItaoz0rkV3X8iLY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Login.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Login.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW\u002Bs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1890749"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW\u002Bs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Login.u5ennpjbay.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Login.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u5ennpjbay"},{"Name":"integrity","Value":"sha256-ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW\u002Bs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Login.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1890749"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022ju26IPf7jQ3PdhCXWOLUCIbTb1S1FKjSXVW1c/fzW\u002Bs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Login2.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Login2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"390213"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Login2.uvazvaoys7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Login2.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uvazvaoys7"},{"Name":"integrity","Value":"sha256-JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Login2.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"390213"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022JvebX3YgM9Gjc8rDsAN5eKzg3axmlNKrciSW3clvx4E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/logo.95njsei89b.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"95njsei89b"},{"Name":"integrity","Value":"sha256-E1C5YkxAxHD\u002Bs7KAP4l4wgPVe4EhOkWrhwmYfp31lRE="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/logo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48171"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022E1C5YkxAxHD\u002Bs7KAP4l4wgPVe4EhOkWrhwmYfp31lRE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/LOGO.ftm9a8wvm2.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\LOGO.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ftm9a8wvm2"},{"Name":"integrity","Value":"sha256-wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/LOGO.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"103145"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/logo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\logo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-E1C5YkxAxHD\u002Bs7KAP4l4wgPVe4EhOkWrhwmYfp31lRE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"48171"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022E1C5YkxAxHD\u002Bs7KAP4l4wgPVe4EhOkWrhwmYfp31lRE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/LOGO.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\LOGO.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"103145"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022wwV2JH0uihEBS1I/dXr1qmZYqSEiym4XSJ3eIMuB0D0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/moving and cleaning.h26kxx62xe.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\moving and cleaning.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h26kxx62xe"},{"Name":"integrity","Value":"sha256-R9\u002Bk3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/moving and cleaning.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2571"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022R9\u002Bk3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/moving and cleaning.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\moving and cleaning.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-R9\u002Bk3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2571"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022R9\u002Bk3y6WyZfSVpUddiYxxuFrKPW0qHtrA/KWWvuQLHA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/moving.08zxpr0k6o.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\moving.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"08zxpr0k6o"},{"Name":"integrity","Value":"sha256-cMrkb\u002BqVwGbDhAEsk4jQ\u002BQwjJP3kglgmXwa1RHnYHM0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/moving.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2074"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022cMrkb\u002BqVwGbDhAEsk4jQ\u002BQwjJP3kglgmXwa1RHnYHM0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/moving.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\moving.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cMrkb\u002BqVwGbDhAEsk4jQ\u002BQwjJP3kglgmXwa1RHnYHM0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2074"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022cMrkb\u002BqVwGbDhAEsk4jQ\u002BQwjJP3kglgmXwa1RHnYHM0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Nathing.6upc1whwmo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Nathing.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6upc1whwmo"},{"Name":"integrity","Value":"sha256-MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Nathing.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9265"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Nathing.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Nathing.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"9265"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022MsGauKaRuqa583D3Aab27pcdmqqMzdch9Kv3hurUbVU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/painting.4nmlm6ldy8.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\painting.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4nmlm6ldy8"},{"Name":"integrity","Value":"sha256-IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/painting.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1571"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/painting.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\painting.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1571"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022IFzlHkoMHSnToaEqbRVLW72gW3Nxl/sbu2lvTMfjTFs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Partner.oiiwkv27sk.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Partner.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oiiwkv27sk"},{"Name":"integrity","Value":"sha256-QIqgz\u002BmHXZXoeh4zXnsGSo9coUm\u002B5Kit5w3vbSpA544="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Partner.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"52265"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022QIqgz\u002BmHXZXoeh4zXnsGSo9coUm\u002B5Kit5w3vbSpA544=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Partner.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Partner.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QIqgz\u002BmHXZXoeh4zXnsGSo9coUm\u002B5Kit5w3vbSpA544="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"52265"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022QIqgz\u002BmHXZXoeh4zXnsGSo9coUm\u002B5Kit5w3vbSpA544=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/pizaa.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\pizaa.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-obyR/G5Lx8H0lsDbHOhl0gVCl6Qu\u002Bv4MXzSWeyFvzCo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"49796"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022obyR/G5Lx8H0lsDbHOhl0gVCl6Qu\u002Bv4MXzSWeyFvzCo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/pizaa.r9shujdryk.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\pizaa.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r9shujdryk"},{"Name":"integrity","Value":"sha256-obyR/G5Lx8H0lsDbHOhl0gVCl6Qu\u002Bv4MXzSWeyFvzCo="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/pizaa.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"49796"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022obyR/G5Lx8H0lsDbHOhl0gVCl6Qu\u002Bv4MXzSWeyFvzCo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/PostFinance.4hsu3t0zv7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\PostFinance.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4hsu3t0zv7"},{"Name":"integrity","Value":"sha256-qCKlVCPPZaUg8GPlxVL3MP\u002BkwsFzZ0rqx1DtWus\u002BVIc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/PostFinance.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3774"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qCKlVCPPZaUg8GPlxVL3MP\u002BkwsFzZ0rqx1DtWus\u002BVIc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/PostFinance.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\PostFinance.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qCKlVCPPZaUg8GPlxVL3MP\u002BkwsFzZ0rqx1DtWus\u002BVIc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3774"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qCKlVCPPZaUg8GPlxVL3MP\u002BkwsFzZ0rqx1DtWus\u002BVIc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Post_Code.pgsifodmme.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Post_Code.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pgsifodmme"},{"Name":"integrity","Value":"sha256-mX/4jdy45NsRcjHViLVn1xne\u002BqFNQA\u002B2eu0x7sUUC\u002Bc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Post_Code.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2469"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mX/4jdy45NsRcjHViLVn1xne\u002BqFNQA\u002B2eu0x7sUUC\u002Bc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Post_Code.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Post_Code.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mX/4jdy45NsRcjHViLVn1xne\u002BqFNQA\u002B2eu0x7sUUC\u002Bc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2469"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022mX/4jdy45NsRcjHViLVn1xne\u002BqFNQA\u002B2eu0x7sUUC\u002Bc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Saldo.lkqj4z6h2q.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Saldo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lkqj4z6h2q"},{"Name":"integrity","Value":"sha256-u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Saldo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4743"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Saldo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Saldo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"4743"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022u2Kwr2KXo3qIIBA/Jaxd/YBcl3wSHFB1NMonNUTcpiA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Schloss.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Schloss.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16\u002BY4ev6M="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"2413"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16\u002BY4ev6M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Schloss.u42ytrqwrl.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Schloss.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"u42ytrqwrl"},{"Name":"integrity","Value":"sha256-spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16\u002BY4ev6M="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Schloss.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2413"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022spciVcJ2EWjd0bYyjYcZi1ILajCY8SR2jb16\u002BY4ev6M=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Stop.i5hczuhaqb.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Stop.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i5hczuhaqb"},{"Name":"integrity","Value":"sha256-KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm\u002BLFHBs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Stop.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7615"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm\u002BLFHBs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Stop.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Stop.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm\u002BLFHBs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7615"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022KlOCel4GwCaP/ClyBZAbVRTaQbPE7CxbqkGYm\u002BLFHBs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/thank-you.22o4zjfsw6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\thank-you.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"22o4zjfsw6"},{"Name":"integrity","Value":"sha256-No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/thank-you.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"968719"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/thank-you.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\thank-you.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"968719"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022No6YrlyHLTe5qjcM8QFjbJ9Z2idCdUPrbsB5ArZmL/I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/thankyou.06v2xtonbi.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\thankyou.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"06v2xtonbi"},{"Name":"integrity","Value":"sha256-SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/thankyou.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"75950"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/thankyou.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\thankyou.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"75950"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022SJ/4wZakyhUkuptLDZRktXrfle14iJO7dfM2cF67FLc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Twint.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Twint.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi\u002B6TwRbWtVysetM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1882"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi\u002B6TwRbWtVysetM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Twint.xag9m31aj6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Twint.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xag9m31aj6"},{"Name":"integrity","Value":"sha256-yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi\u002B6TwRbWtVysetM="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Twint.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1882"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022yQxjtf5ZFzgwAPiT53kFUGvFHg7Qi\u002B6TwRbWtVysetM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/us-flag.6rnqyqozqm.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\us-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6rnqyqozqm"},{"Name":"integrity","Value":"sha256-wkRNKPLm/0134O8A2OXzaNfAFIDTxFK\u002BeSQCEPhPsB4="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/us-flag.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24631"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wkRNKPLm/0134O8A2OXzaNfAFIDTxFK\u002BeSQCEPhPsB4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/us-flag.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\us-flag.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wkRNKPLm/0134O8A2OXzaNfAFIDTxFK\u002BeSQCEPhPsB4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"24631"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022wkRNKPLm/0134O8A2OXzaNfAFIDTxFK\u002BeSQCEPhPsB4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Visa_Konto.6yr6353tw7.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Visa_Konto.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6yr6353tw7"},{"Name":"integrity","Value":"sha256-AqYCkkCKKf2n\u002BzRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/img/Visa_Konto.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7240"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AqYCkkCKKf2n\u002BzRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/img/Visa_Konto.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\img\Visa_Konto.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AqYCkkCKKf2n\u002BzRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"7240"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022AqYCkkCKKf2n\u002BzRXmeRtcU1/uQXS7ImrdvSIJ0/hpsg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/js/config.jrtn85pb04.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\js\config.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jrtn85pb04"},{"Name":"integrity","Value":"sha256-b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/js/config.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"753"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/js/config.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\js\config.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"753"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022b4I4UbhUid0FGfY/FZMhucoNeiPUZiuyX51y/wZps6U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/js/main.ey5eontg2m.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\js\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ey5eontg2m"},{"Name":"integrity","Value":"sha256-prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/js/main.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3789"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/js/main.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\js\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3789"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022prl4KIIKB0JqbbXfjcgLFh/3EmuKhR5RvfUJctuqYc0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/css/core.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\css\core.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-21uvDVBawlNTpG\u002BD5VMfC1bb/9QhPhnjE1ACKek8FMs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"986733"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002221uvDVBawlNTpG\u002BD5VMfC1bb/9QhPhnjE1ACKek8FMs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/css/core.z666vu0l64.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\css\core.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z666vu0l64"},{"Name":"integrity","Value":"sha256-21uvDVBawlNTpG\u002BD5VMfC1bb/9QhPhnjE1ACKek8FMs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/css/core.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"986733"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002221uvDVBawlNTpG\u002BD5VMfC1bb/9QhPhnjE1ACKek8FMs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/css/page-auth.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\css\page-auth.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MpcNIJxfGjwnslj6av\u002Br2qwj/dzHMiSkOfxbc5VHFOs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16195"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022MpcNIJxfGjwnslj6av\u002Br2qwj/dzHMiSkOfxbc5VHFOs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/css/page-auth.mnmed00ooq.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\css\page-auth.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mnmed00ooq"},{"Name":"integrity","Value":"sha256-MpcNIJxfGjwnslj6av\u002Br2qwj/dzHMiSkOfxbc5VHFOs="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/css/page-auth.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16195"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022MpcNIJxfGjwnslj6av\u002Br2qwj/dzHMiSkOfxbc5VHFOs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/css/theme-default.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\css\theme-default.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"74010"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/css/theme-default.dk2ytlcajt.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\css\theme-default.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dk2ytlcajt"},{"Name":"integrity","Value":"sha256-fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/css/theme-default.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"74010"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022fTJ53gfOzMoueTyi96LoXEk6yAfFODjoHe79ydgHOVg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons.4hzulchcxt.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4hzulchcxt"},{"Name":"integrity","Value":"sha256-6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD\u002BryrU="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"79999"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00226PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD\u002BryrU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD\u002BryrU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"79999"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00226PtP/PAu3KHfRD5F/OZci1sJmwuz4PclKdyVpD\u002BryrU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.1oivsu8mcf.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1oivsu8mcf"},{"Name":"integrity","Value":"sha256-QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.woff"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"292480"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.5j3plm5gu1.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5j3plm5gu1"},{"Name":"integrity","Value":"sha256-34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.woff2"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"102988"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u002234RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"292572"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.q9t8biukf5.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"q9t8biukf5"},{"Name":"integrity","Value":"sha256-Lpkz1ATMc/40f\u002Bahhgq7eGVTP83nN2WgwqKSph5k2wo="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.ttf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"292404"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Lpkz1ATMc/40f\u002Bahhgq7eGVTP83nN2WgwqKSph5k2wo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.rri4hp3ggs.eot">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.eot'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"rri4hp3ggs"},{"Name":"integrity","Value":"sha256-Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.eot"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"292572"},{"Name":"Content-Type","Value":"application/vnd.ms-fontobject"},{"Name":"ETag","Value":"\u0022Bx0iFactVs1XqMnGSXNgQHykpAgae6y4BaEOGtip7PU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002BVSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1125137"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022\u002BVSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.ttf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.ttf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Lpkz1ATMc/40f\u002Bahhgq7eGVTP83nN2WgwqKSph5k2wo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"292404"},{"Name":"Content-Type","Value":"application/x-font-ttf"},{"Name":"ETag","Value":"\u0022Lpkz1ATMc/40f\u002Bahhgq7eGVTP83nN2WgwqKSph5k2wo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.woff">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.woff'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"292480"},{"Name":"Content-Type","Value":"application/font-woff"},{"Name":"ETag","Value":"\u0022QUTt2cNsIJkbGnEWpEpwnZlrpeMI1ScfSQeTJdFKv7Q=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.woff2">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.woff2'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-34RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"102988"},{"Name":"Content-Type","Value":"font/woff2"},{"Name":"ETag","Value":"\u002234RYJip9PU2tKFFlXeKzujtxH1Liv85jzgNIcwu4Gcc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.x6kftl55k2.svg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\fonts\boxicons\boxicons.svg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x6kftl55k2"},{"Name":"integrity","Value":"sha256-\u002BVSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/fonts/boxicons/boxicons.svg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1125137"},{"Name":"Content-Type","Value":"image/svg\u002Bxml"},{"Name":"ETag","Value":"\u0022\u002BVSgQwtBSscYXjLRa66DCDyS2wF0SeFmwY8rnLntP1U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/jquery/jquery.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\jquery\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th\u002BKr6HkYM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"832977"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th\u002BKr6HkYM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/jquery/jquery.v0zti25kgd.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\jquery\jquery.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"v0zti25kgd"},{"Name":"integrity","Value":"sha256-cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th\u002BKr6HkYM="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/jquery/jquery.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"832977"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cb/bjNYToPeDvDNynkjSvinOLAoB12Vc6Th\u002BKr6HkYM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/bootstrap.gv7dia6x1y.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gv7dia6x1y"},{"Name":"integrity","Value":"sha256-5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/js/bootstrap.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"114929"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00225Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/bootstrap.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\bootstrap.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"114929"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00225Dx1Vix9ycXK46KMgwa4gGIQxADuq1GQf6iW9W6GIjg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/helpers.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\helpers.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"103079"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/helpers.nnhel8mq3c.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\helpers.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nnhel8mq3c"},{"Name":"integrity","Value":"sha256-HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/js/helpers.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"103079"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HXrFFoLZabWsBv3KEyINijNnu/c4dtre5twTWNMN72E=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/menu.9h18ncb23z.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\menu.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9h18ncb23z"},{"Name":"integrity","Value":"sha256-g\u002B\u002BrCLb8zL7EX8p2ZBiNPm/M\u002BTlZA75BaVzDqpvDIVM="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/js/menu.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"80183"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022g\u002B\u002BrCLb8zL7EX8p2ZBiNPm/M\u002BTlZA75BaVzDqpvDIVM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/menu.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\menu.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-g\u002B\u002BrCLb8zL7EX8p2ZBiNPm/M\u002BTlZA75BaVzDqpvDIVM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"80183"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022g\u002B\u002BrCLb8zL7EX8p2ZBiNPm/M\u002BTlZA75BaVzDqpvDIVM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/template-customizer.3r8b2a0bt1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\template-customizer.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3r8b2a0bt1"},{"Name":"integrity","Value":"sha256-8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/js/template-customizer.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00228ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/js/template-customizer.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\js\template-customizer.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00228ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\perfect-scrollbar\perfect-scrollbar.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5008"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\perfect-scrollbar\perfect-scrollbar.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"108747"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00228mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.m8j3cetw7e.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\perfect-scrollbar\perfect-scrollbar.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"m8j3cetw7e"},{"Name":"integrity","Value":"sha256-Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5008"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Jti2qrM0g65NYKkwOIi8T09Zmf0GukGJuQknZS4L/Iw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.mibndavuub.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\perfect-scrollbar\perfect-scrollbar.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mibndavuub"},{"Name":"integrity","Value":"sha256-8mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/perfect-scrollbar/perfect-scrollbar.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"108747"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00228mDv4eiuJCHTCzjnz2G/MxNQyE0UhRporarxN2CEvS0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/popper/popper.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\popper\popper.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"57748"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Dashboard/assets/vendor/popper/popper.lln95dcem0.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Dashboard\assets\vendor\popper\popper.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lln95dcem0"},{"Name":"integrity","Value":"sha256-R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk="},{"Name":"label","Value":"_content/TaskDotNet/Dashboard/assets/vendor/popper/popper.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"57748"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022R2f8gp75bjn7zoUXi9LxF4C4/zrBY8MFzpR3h38Fenk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/favicon.61n19gt1b8.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"61n19gt1b8"},{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="},{"Name":"label","Value":"_content/TaskDotNet/favicon.ico"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:46 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/favicon.ico">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\favicon.ico'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"5430"},{"Name":"Content-Type","Value":"image/x-icon"},{"Name":"ETag","Value":"\u0022Jtxf9L\u002B5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:46 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/js/site.hi2kjbe51q.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hi2kjbe51q"},{"Name":"integrity","Value":"sha256-uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI\u002B7fhODQ="},{"Name":"label","Value":"_content/TaskDotNet/js/site.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1456"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI\u002B7fhODQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/js/site.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\js\site.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI\u002B7fhODQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1456"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022uWjtMtYC5kszJ1cwKh0NmtSCsNWSiRtuFhVI\u002B7fhODQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/flatpickr/flatpickr.min.5i5ypew3r7.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\flatpickr\flatpickr.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5i5ypew3r7"},{"Name":"integrity","Value":"sha256-oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c="},{"Name":"label","Value":"_content/TaskDotNet/lib/flatpickr/flatpickr.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"58061"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/flatpickr/flatpickr.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\flatpickr\flatpickr.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23051"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/flatpickr/flatpickr.min.gno4lkuonv.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\flatpickr\flatpickr.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gno4lkuonv"},{"Name":"integrity","Value":"sha256-NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg="},{"Name":"label","Value":"_content/TaskDotNet/lib/flatpickr/flatpickr.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23051"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022NMWQZRHPM5ffxy8dRoyH7oxIu6MYvA73jKaxUlFrHPg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/flatpickr/flatpickr.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\flatpickr\flatpickr.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"58061"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022oz6OpgDwrizfPiuCTQX5Ug80vo7DGQOJb2A4nL67q6c=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-ui/jquery-ui.5tbpznqf90.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-ui\jquery-ui.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5tbpznqf90"},{"Name":"integrity","Value":"sha256-CSeOsMhq\u002BM856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-ui/jquery-ui.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"549952"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CSeOsMhq\u002BM856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-ui/jquery-ui.89idfrxomp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-ui\jquery-ui.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"89idfrxomp"},{"Name":"integrity","Value":"sha256-rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-ui/jquery-ui.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"43221"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-ui/jquery-ui.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-ui\jquery-ui.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"43221"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022rGh6mkaaOyd/13on2ZiAFbzoLj49snCWUDCR0RtG6Q8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-ui/jquery-ui.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-ui\jquery-ui.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-CSeOsMhq\u002BM856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"549952"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022CSeOsMhq\u002BM856gm/B5dxlHOaggTUiPMq7vwBF5YbKmA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.0td7jq9nxb.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0td7jq9nxb"},{"Name":"integrity","Value":"sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19366"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"19366"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zV8SHd\u002Bo2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5868"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022zV8SHd\u002Bo2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.zsyoy62yqm.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zsyoy62yqm"},{"Name":"integrity","Value":"sha256-zV8SHd\u002Bo2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5868"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022zV8SHd\u002Bo2hq7FLST9WlWzpZMGfniOYeMMrQT6lTxjls=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation-unobtrusive/LICENSE.4ez5mqxv2b.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4ez5mqxv2b"},{"Name":"integrity","Value":"sha256-bJBFwc85FxNITit\u002B4\u002Bdmzq5fRpbBEauvU\u002B1eI\u002BEpziQ="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation-unobtrusive/LICENSE.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"575"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022bJBFwc85FxNITit\u002B4\u002Bdmzq5fRpbBEauvU\u002B1eI\u002BEpziQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation-unobtrusive/LICENSE.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation-unobtrusive\LICENSE.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bJBFwc85FxNITit\u002B4\u002Bdmzq5fRpbBEauvU\u002B1eI\u002BEpziQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"575"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022bJBFwc85FxNITit\u002B4\u002Bdmzq5fRpbBEauvU\u002B1eI\u002BEpziQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/additional-methods.gcjdx5jb8l.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gcjdx5jb8l"},{"Name":"integrity","Value":"sha256-0HeMWyQUbTbe7SGnSYbjj9\u002BHVA0hKDmEUtbYoTKe\u002BBk="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation/dist/additional-methods.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51466"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00220HeMWyQUbTbe7SGnSYbjj9\u002BHVA0hKDmEUtbYoTKe\u002BBk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/additional-methods.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0HeMWyQUbTbe7SGnSYbjj9\u002BHVA0hKDmEUtbYoTKe\u002BBk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51466"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00220HeMWyQUbTbe7SGnSYbjj9\u002BHVA0hKDmEUtbYoTKe\u002BBk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/additional-methods.min.35gxhxa0gh.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"35gxhxa0gh"},{"Name":"integrity","Value":"sha256-90Rlzuz8pIZK4g9o2I7nGK\u002B9n5cU9Rbdt4GtxRO5arA="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation/dist/additional-methods.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22174"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002290Rlzuz8pIZK4g9o2I7nGK\u002B9n5cU9Rbdt4GtxRO5arA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/additional-methods.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\additional-methods.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-90Rlzuz8pIZK4g9o2I7nGK\u002B9n5cU9Rbdt4GtxRO5arA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22174"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002290Rlzuz8pIZK4g9o2I7nGK\u002B9n5cU9Rbdt4GtxRO5arA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/jquery.validate.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"51171"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"b7iojwaux1"},{"Name":"integrity","Value":"sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation/dist/jquery.validate.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24601"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/jquery.validate.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"24601"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\dist\jquery.validate.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pzqfkb6aqo"},{"Name":"integrity","Value":"sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation/dist/jquery.validate.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"51171"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/LICENSE.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1095"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u002285iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/jquery-validation/LICENSE.xzw0cte36n.md">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\jquery-validation\LICENSE.md'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xzw0cte36n"},{"Name":"integrity","Value":"sha256-85iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA="},{"Name":"label","Value":"_content/TaskDotNet/lib/jquery-validation/LICENSE.md"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1095"},{"Name":"Content-Type","Value":"text/markdown"},{"Name":"ETag","Value":"\u002285iHjKszi4aWOL2sGurna/OsEbK4nabgtovBpkVzNEA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/sweetalert2/sweet-alerts.init.1hy9sozciy.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\sweetalert2\sweet-alerts.init.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"1hy9sozciy"},{"Name":"integrity","Value":"sha256-O713m4S\u002BNla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM="},{"Name":"label","Value":"_content/TaskDotNet/lib/sweetalert2/sweet-alerts.init.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5186"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022O713m4S\u002BNla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/sweetalert2/sweet-alerts.init.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\sweetalert2\sweet-alerts.init.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O713m4S\u002BNla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5186"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022O713m4S\u002BNla9Fz3hm7uRVYXjm3RKdyjANyAcMxQUMqM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/sweetalert2/sweetalert2.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\sweetalert2\sweetalert2.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"40533"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/sweetalert2/sweetalert2.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\sweetalert2\sweetalert2.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hN/E1DmwAKy7yKvTL\u002BrrKp\u002B5jow0GskgNjaiFv4bGNI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"79229"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022hN/E1DmwAKy7yKvTL\u002BrrKp\u002B5jow0GskgNjaiFv4bGNI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/sweetalert2/sweetalert2.min.ka04m46apy.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\sweetalert2\sweetalert2.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ka04m46apy"},{"Name":"integrity","Value":"sha256-hN/E1DmwAKy7yKvTL\u002BrrKp\u002B5jow0GskgNjaiFv4bGNI="},{"Name":"label","Value":"_content/TaskDotNet/lib/sweetalert2/sweetalert2.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"79229"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022hN/E1DmwAKy7yKvTL\u002BrrKp\u002B5jow0GskgNjaiFv4bGNI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/sweetalert2/sweetalert2.min.o9isx86jyx.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\sweetalert2\sweetalert2.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o9isx86jyx"},{"Name":"integrity","Value":"sha256-hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA="},{"Name":"label","Value":"_content/TaskDotNet/lib/sweetalert2/sweetalert2.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"40533"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hLMJZipQRPPE02iB4hqYxrGKht4CPqqm4hXfxnJ1RYA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/icons/default/icons.min.a311uwf1bl.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\icons\default\icons.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a311uwf1bl"},{"Name":"integrity","Value":"sha256-OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/icons/default/icons.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"67093"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/icons/default/icons.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\icons\default\icons.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"67093"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022OrZKRXNWpYLJeuB40gXMbY1NYzcDdjTWhHMWRrx3S0w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/langs/de.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\langs\de.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14915"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/langs/de.wvtppe077w.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\langs\de.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wvtppe077w"},{"Name":"integrity","Value":"sha256-jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/langs/de.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14915"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jNQyFHX7GGkWkNQd6l7k/TBBYaukmJG/lGf2OgKPRlg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/langs/fr.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\langs\fr.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"15498"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/langs/fr.qch6z36znb.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\langs\fr.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qch6z36znb"},{"Name":"integrity","Value":"sha256-WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/langs/fr.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15498"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WqwlPjuN4XA63pipcPv3eVWtZFCBMkLV/CCkgY7QjYY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/langs/it.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\langs\it.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14864"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00220R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/langs/it.xwf5nig97g.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\langs\it.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"xwf5nig97g"},{"Name":"integrity","Value":"sha256-0R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/langs/it.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14864"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00220R45OOMQehpekdzjm9XFoAhlY6XP5tgdhCuPnV1gf88=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/license.6q9vckcrvl.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\license.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6q9vckcrvl"},{"Name":"integrity","Value":"sha256-AuFIf7NMpoTAxHiBJGeJ\u002BfnLu3ZdewyqbfBPQmvu6Ss="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/license.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1123"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022AuFIf7NMpoTAxHiBJGeJ\u002BfnLu3ZdewyqbfBPQmvu6Ss=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/license.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\license.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AuFIf7NMpoTAxHiBJGeJ\u002BfnLu3ZdewyqbfBPQmvu6Ss="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1123"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022AuFIf7NMpoTAxHiBJGeJ\u002BfnLu3ZdewyqbfBPQmvu6Ss=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/models/dom/model.min.duy7y4d4ys.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\models\dom\model.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"duy7y4d4ys"},{"Name":"integrity","Value":"sha256-JW4mmSPFbukV\u002BMlXJt4j3\u002BCk9/YvSjsTIeWxKqhApYw="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/models/dom/model.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"96635"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022JW4mmSPFbukV\u002BMlXJt4j3\u002BCk9/YvSjsTIeWxKqhApYw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/models/dom/model.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\models\dom\model.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JW4mmSPFbukV\u002BMlXJt4j3\u002BCk9/YvSjsTIeWxKqhApYw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"96635"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022JW4mmSPFbukV\u002BMlXJt4j3\u002BCk9/YvSjsTIeWxKqhApYw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/advlist/plugin.min.8mf5u9u62l.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\advlist\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8mf5u9u62l"},{"Name":"integrity","Value":"sha256-sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/advlist/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3596"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/advlist/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\advlist\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3596"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022sB88l2veKSasFiX6UwgdOgItMhHOnvbYD1EI41o9ZKQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/anchor/plugin.min.jgmmjjao6x.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\anchor\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jgmmjjao6x"},{"Name":"integrity","Value":"sha256-M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/anchor/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2519"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/anchor/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\anchor\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2519"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022M9rXE4zOfBNceqEEVYRF9Wc0eJFKN5nN6XJsqouEm9w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autolink/plugin.min.5j7akqptjm.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\autolink\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5j7akqptjm"},{"Name":"integrity","Value":"sha256-X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB\u002BEALh70nY="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autolink/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3262"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB\u002BEALh70nY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autolink/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\autolink\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB\u002BEALh70nY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3262"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022X6/iPTXo3DHoRrhIBYx3G11EP9yoqNxuEB\u002BEALh70nY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\autoresize\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2523"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.prxhfvklc1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\autoresize\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"prxhfvklc1"},{"Name":"integrity","Value":"sha256-NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autoresize/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2523"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022NZDPk3nIjTd5kYSwUjepixD/wI3Pg2xE/Z0vwbw5ty8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autosave/plugin.min.cjafcovg7d.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\autosave\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cjafcovg7d"},{"Name":"integrity","Value":"sha256-GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autosave/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3331"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/autosave/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\autosave\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3331"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022GL9/sUtFd7E90S/DlSlb1k6CZ07rPy8AxoRv6HYPdUc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/charmap/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\charmap\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11014"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/charmap/plugin.min.k7ldt4dufz.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\charmap\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k7ldt4dufz"},{"Name":"integrity","Value":"sha256-YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/charmap/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11014"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YDZqO1M5WTzWCRJFvlCjvgTNAyHWEtGIZiWCMg/q7I4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/code/plugin.min.4ddczsiarp.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\code\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4ddczsiarp"},{"Name":"integrity","Value":"sha256-WJ7/J6y6iN/ejUeEuBKwRHYYr19C\u002BdswBchCCc4i4dc="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/code/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"880"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WJ7/J6y6iN/ejUeEuBKwRHYYr19C\u002BdswBchCCc4i4dc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/code/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\code\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WJ7/J6y6iN/ejUeEuBKwRHYYr19C\u002BdswBchCCc4i4dc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"880"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WJ7/J6y6iN/ejUeEuBKwRHYYr19C\u002BdswBchCCc4i4dc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/codesample/plugin.min.c8susjty0k.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\codesample\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c8susjty0k"},{"Name":"integrity","Value":"sha256-lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/codesample/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"48071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/codesample/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\codesample\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"48071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022lP14sGZBt232AXx2IgiffG05ToLDl94/8gelcAqKz/k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/directionality/plugin.min.3a71kovr6i.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\directionality\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3a71kovr6i"},{"Name":"integrity","Value":"sha256-G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/directionality/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4363"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/directionality/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\directionality\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4363"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022G38CauAcr2c29p6hTsmx7Vf7g8aVD3XLMraDzT1LFkg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojiimages.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"410112"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.liga2smdmf.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojiimages.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"liga2smdmf"},{"Name":"integrity","Value":"sha256-HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"410112"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022HUSrMDE/gPFhzb1KoZuCEGxbRLDTVw7EK0xrnLV2MLw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojiimages.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"416097"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.yu9bjgbclx.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojiimages.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yu9bjgbclx"},{"Name":"integrity","Value":"sha256-VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojiimages.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"416097"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022VhbNgwleZ0aBrlDYhC0HoCXNM/tFWw334aHWDzsX7X4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojis.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cd/Y1gLoV\u002B\u002BSs2\u002BuK/pBed5ezvmZvvkKUbiNZlAfeNg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"186921"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cd/Y1gLoV\u002B\u002BSs2\u002BuK/pBed5ezvmZvvkKUbiNZlAfeNg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojis.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9qA\u002Bsvinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"192857"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229qA\u002Bsvinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.uvtj9eiepa.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojis.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uvtj9eiepa"},{"Name":"integrity","Value":"sha256-9qA\u002Bsvinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"192857"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229qA\u002Bsvinem//r5QpAqi0hY5wSTsRMxZYDlVFDiqxHUk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.zrqg1ly84r.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\js\emojis.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zrqg1ly84r"},{"Name":"integrity","Value":"sha256-cd/Y1gLoV\u002B\u002BSs2\u002BuK/pBed5ezvmZvvkKUbiNZlAfeNg="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/js/emojis.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"186921"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cd/Y1gLoV\u002B\u002BSs2\u002BuK/pBed5ezvmZvvkKUbiNZlAfeNg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-O6NJFEHcJ\u002BC0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6393"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022O6NJFEHcJ\u002BC0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.tyqdbacaxt.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\emoticons\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"tyqdbacaxt"},{"Name":"integrity","Value":"sha256-O6NJFEHcJ\u002BC0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/emoticons/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6393"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022O6NJFEHcJ\u002BC0px3Y2uzYyU7A9CGNi5anTyYRX0nBm/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.h0f14fag6o.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\fullscreen\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"h0f14fag6o"},{"Name":"integrity","Value":"sha256-QcJ3NX\u002BBjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14724"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QcJ3NX\u002BBjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/fullscreen/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\fullscreen\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QcJ3NX\u002BBjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14724"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022QcJ3NX\u002BBjeJwiDpHt7zSuredna6DxB4sJjLbd5W9jAw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/help/plugin.min.44sxdvkzh1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\help\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"44sxdvkzh1"},{"Name":"integrity","Value":"sha256-bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/help/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"14146"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/help/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\help\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"14146"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022bOqLAZv7afzVj3xwnDmzzxhShG2oP5ULhWytG5x1FzM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/image/plugin.min.cblduhn0ke.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\image\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cblduhn0ke"},{"Name":"integrity","Value":"sha256-KN/12ahHVS1ShB0dbnggNsZbe05\u002Ba6TxiB9U8Z0Hcm4="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/image/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"19345"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022KN/12ahHVS1ShB0dbnggNsZbe05\u002Ba6TxiB9U8Z0Hcm4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/image/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\image\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KN/12ahHVS1ShB0dbnggNsZbe05\u002Ba6TxiB9U8Z0Hcm4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"19345"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022KN/12ahHVS1ShB0dbnggNsZbe05\u002Ba6TxiB9U8Z0Hcm4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/importcss/plugin.min.47anb2mjko.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\importcss\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"47anb2mjko"},{"Name":"integrity","Value":"sha256-YOYNs4IzRdIEB9dGXNeF4\u002BxuMtua3xaUzIgtruEYDRY="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/importcss/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4056"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YOYNs4IzRdIEB9dGXNeF4\u002BxuMtua3xaUzIgtruEYDRY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/importcss/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\importcss\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YOYNs4IzRdIEB9dGXNeF4\u002BxuMtua3xaUzIgtruEYDRY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4056"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022YOYNs4IzRdIEB9dGXNeF4\u002BxuMtua3xaUzIgtruEYDRY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\insertdatetime\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-9rVcmFSBroQ24NjkfK\u002B7PfynK30dkzpwA4j7PjXNLV8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2886"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229rVcmFSBroQ24NjkfK\u002B7PfynK30dkzpwA4j7PjXNLV8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.qv8sml9f80.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\insertdatetime\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qv8sml9f80"},{"Name":"integrity","Value":"sha256-9rVcmFSBroQ24NjkfK\u002B7PfynK30dkzpwA4j7PjXNLV8="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/insertdatetime/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2886"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00229rVcmFSBroQ24NjkfK\u002B7PfynK30dkzpwA4j7PjXNLV8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/link/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\link\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"15655"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/link/plugin.min.l1bjazi1ht.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\link\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l1bjazi1ht"},{"Name":"integrity","Value":"sha256-qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/link/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15655"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022qdTUMHQ1ivMAFqkbnzyLjg2G2Lw2B0BFmnktKidGdBo=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/lists/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\lists\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf\u002BEkYTIw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"24503"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf\u002BEkYTIw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/lists/plugin.min.pbb8yvsmll.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\lists\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pbb8yvsmll"},{"Name":"integrity","Value":"sha256-q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf\u002BEkYTIw="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/lists/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"24503"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022q5ZgdHCBZQ/KgkREVuLMEvNl8DRZqDr2Srzf\u002BEkYTIw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/media/plugin.min.7iyho1y9tr.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\media\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7iyho1y9tr"},{"Name":"integrity","Value":"sha256-7\u002BRnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/media/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16698"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00227\u002BRnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/media/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\media\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-7\u002BRnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16698"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00227\u002BRnW02dMHJvkS6j43M/XM7ARJaREuqlDmv56akcSwE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\nonbreaking\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1419"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.zlpcbzoevc.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\nonbreaking\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zlpcbzoevc"},{"Name":"integrity","Value":"sha256-WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/nonbreaking/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1419"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022WZwYNun61Lm2ws06s18mAs/AvYzrr1ios6vNaZIZVVg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\pagebreak\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NHGv4s6mg2R/cI\u002BSc\u002BllgOQTEuBxCFcWgA7XO4s5wQs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1509"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022NHGv4s6mg2R/cI\u002BSc\u002BllgOQTEuBxCFcWgA7XO4s5wQs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.ovwbmcvqah.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\pagebreak\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ovwbmcvqah"},{"Name":"integrity","Value":"sha256-NHGv4s6mg2R/cI\u002BSc\u002BllgOQTEuBxCFcWgA7XO4s5wQs="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/pagebreak/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1509"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022NHGv4s6mg2R/cI\u002BSc\u002BllgOQTEuBxCFcWgA7XO4s5wQs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/preview/plugin.min.6lkls1wqwx.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\preview\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6lkls1wqwx"},{"Name":"integrity","Value":"sha256-huRPOwMMtY\u002BMMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/preview/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1721"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022huRPOwMMtY\u002BMMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/preview/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\preview\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-huRPOwMMtY\u002BMMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1721"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022huRPOwMMtY\u002BMMuzfPGVPxjZUeQGltOOkZW8rz5Il5Kc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.3qkdkjqh6m.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\quickbars\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3qkdkjqh6m"},{"Name":"integrity","Value":"sha256-K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/quickbars/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\quickbars\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5071"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022K0ps/5joHKY9GZlryyMCpNUw99ak1IuWHXWJTh22uKQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/save/plugin.min.39ecmwg4fk.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\save\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"39ecmwg4fk"},{"Name":"integrity","Value":"sha256-2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/save/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1595"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00222dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/save/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\save\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1595"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u00222dbVtLEsfZ2352U4i1aW0fuWKhVe65ROOQW3S1abmik=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.hd8f6h4flh.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\searchreplace\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hd8f6h4flh"},{"Name":"integrity","Value":"sha256-230LS7ZgEYDlOs8uqnxzpIKgvz8\u002BOM7f3HBwWyLBjR4="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"13420"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022230LS7ZgEYDlOs8uqnxzpIKgvz8\u002BOM7f3HBwWyLBjR4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/searchreplace/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\searchreplace\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-230LS7ZgEYDlOs8uqnxzpIKgvz8\u002BOM7f3HBwWyLBjR4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"13420"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022230LS7ZgEYDlOs8uqnxzpIKgvz8\u002BOM7f3HBwWyLBjR4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/table/plugin.min.gt40yiyx87.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\table\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gt40yiyx87"},{"Name":"integrity","Value":"sha256-ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/table/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"47313"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/table/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\table\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"47313"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ZGIB1LWXJ3klIWlgODDC4bv5gWbNfOJREI0zGq/Pjwk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/template/plugin.min.3jv5k5vtg9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\template\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3jv5k5vtg9"},{"Name":"integrity","Value":"sha256-IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/template/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8262"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/template/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\template\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8262"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022IdmBFLikOBbH3OD3ocUMDgphC7jurcOkFI4e5YbG9f0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.gbx3kasexj.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\visualblocks\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gbx3kasexj"},{"Name":"integrity","Value":"sha256-bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1233"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/visualblocks/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\visualblocks\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1233"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022bAe4i6oJLcx6T2RXYh9KGSCiHKbrwTZW9mBz1Rxw85w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.4igg1krqg4.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\visualchars\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4igg1krqg4"},{"Name":"integrity","Value":"sha256-jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm\u002BHijgbZpw="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5872"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm\u002BHijgbZpw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/visualchars/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\visualchars\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm\u002BHijgbZpw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5872"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022jFjjJIxMlLkRAawsiueK5ENxrPCTNk6RCm\u002BHijgbZpw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\wordcount\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"11922"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002203uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.ra9jyp21g1.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\plugins\wordcount\plugin.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ra9jyp21g1"},{"Name":"integrity","Value":"sha256-03uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/plugins/wordcount/plugin.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11922"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002203uPxSjBw6NtgZQ7Ie7kntIU6LMRSfyG7fRvazN/WYc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/dark/content.min.c0bbxs3g2a.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"c0bbxs3g2a"},{"Name":"integrity","Value":"sha256-hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/dark/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1217"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/dark/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1217"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022hBL1swjQ8ATLOhDRSdYFftp9VSHp1C6wKU99Up96EtU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/default/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\default\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1150"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/default/content.min.dehv3jch66.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\default\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dehv3jch66"},{"Name":"integrity","Value":"sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/default/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1150"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/document/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\document\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m\u002BIxm4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1249"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m\u002BIxm4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/document/content.min.qcb3yuv502.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\document\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"qcb3yuv502"},{"Name":"integrity","Value":"sha256-OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m\u002BIxm4="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/document/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1249"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022OibrZO1BwCZBqzlqLa49cmMjZScEWisnx8vW4m\u002BIxm4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\tinymce-5-dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1220"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00222K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.o43ooccw0k.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\tinymce-5-dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o43ooccw0k"},{"Name":"integrity","Value":"sha256-2K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/tinymce-5-dark/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1220"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u00222K1UjR5hvgdA7exHkd7cGxyDlx5xMN4Mq7DbANzRW74=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\tinymce-5\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1150"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.dehv3jch66.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\tinymce-5\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dehv3jch66"},{"Name":"integrity","Value":"sha256-K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/tinymce-5/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1150"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022K4dNjU0rJHiKbagAhzo/IXhAftfGUbWOdolvsRM3VJA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/writer/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\writer\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1171"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/writer/content.min.l2coynlc21.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\content\writer\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"l2coynlc21"},{"Name":"integrity","Value":"sha256-HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/content/writer/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1171"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022HcG61xkSF80A9nX7joEfy78oze7ipNnWJLcZFNyXkN0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.9a0ytxc496.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9a0ytxc496"},{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.inline.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.brznt3mq19.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"brznt3mq19"},{"Name":"integrity","Value":"sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22932"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22932"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"72948"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.uukcty6ctu.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uukcty6ctu"},{"Name":"integrity","Value":"sha256-lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"72948"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022lln0brrr3DgD34KEMqozIoVfjQJLAe4d5TFN7bH2qng=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.oy4aiorfkf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide-dark\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oy4aiorfkf"},{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.9a0ytxc496.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9a0ytxc496"},{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/content.inline.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/content.min.57uyr91r6a.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"57uyr91r6a"},{"Name":"integrity","Value":"sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23321"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23321"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"72986"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.eoxyrui399.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eoxyrui399"},{"Name":"integrity","Value":"sha256-Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/skin.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"72986"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022Ag7NY4ZCHA7owxGaXnJXIelRzmBMSzCHDJ1rT0Abl4I=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.oy4aiorfkf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\oxide\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oy4aiorfkf"},{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/oxide/skin.shadowdom.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.9a0ytxc496.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9a0ytxc496"},{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.brznt3mq19.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"brznt3mq19"},{"Name":"integrity","Value":"sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"22932"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"22932"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022xx1rh/bfkbsbYBb4BEkDn2qvKyEQaJq34YO1ZBLxJho=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"75477"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.hdz6qpdk9t.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hdz6qpdk9t"},{"Name":"integrity","Value":"sha256-a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"75477"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022a9n45qBDLmPlsAhDtfoZGv2hNHCVzT2FrFGYPVUf4fs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.oy4aiorfkf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oy4aiorfkf"},{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.9a0ytxc496.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9a0ytxc496"},{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.inline.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\content.inline.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23262"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022D6mSXtRZaKfTBatlB3\u002BxTIvbM/xFj9oYMv7r/64dMEs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.57uyr91r6a.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"57uyr91r6a"},{"Name":"integrity","Value":"sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23321"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/content.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\content.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23321"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022n6PVrWeBqb3eUtxsDO3m781iJd/NRVAHyf1r2fadcgY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.39r29kkvt5.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"39r29kkvt5"},{"Name":"integrity","Value":"sha256-iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"75630"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\skin.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"75630"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022iZDK3SFJICO3Vp0rP4MNGgUbVVigRqJXSWlENm0IGS4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.oy4aiorfkf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\skins\ui\tinymce-5\skin.shadowdom.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"oy4aiorfkf"},{"Name":"integrity","Value":"sha256-YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"509"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022YKY/t4YnrGSGpb14Pi3NT0rpUW7g3CqGcXk0zWCH9rM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/themes/silver/theme.min.gm9r9yi2u9.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\themes\silver\theme.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gm9r9yi2u9"},{"Name":"integrity","Value":"sha256-iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/themes/silver/theme.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"397436"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/themes/silver/theme.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\themes\silver\theme.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"397436"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022iIUuMQxnPVeQVw6MdduCoP8CtL8tz8Xk1GUh5XTFYx8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/tinymce.d.0uz5kw6u8m.ts">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\tinymce.d.ts'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"0uz5kw6u8m"},{"Name":"integrity","Value":"sha256-qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/tinymce.d.ts"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"127423"},{"Name":"Content-Type","Value":"video/vnd.dlna.mpeg-tts"},{"Name":"ETag","Value":"\u0022qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/tinymce.d.ts">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\tinymce.d.ts'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"127423"},{"Name":"Content-Type","Value":"video/vnd.dlna.mpeg-tts"},{"Name":"ETag","Value":"\u0022qDg/T/2gGYkWtTcrrVfwPOmw2KkcVFmPuZLdPeHBomE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/tinymce.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\tinymce.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lOTcZxqVz8iqrI9BFx\u002BQBG4pK4KRYH0gFUcExOiM4UE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"415254"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022lOTcZxqVz8iqrI9BFx\u002BQBG4pK4KRYH0gFUcExOiM4UE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/lib/tinymce/js/tinymce/tinymce.min.k214tg81kt.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\lib\tinymce\js\tinymce\tinymce.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k214tg81kt"},{"Name":"integrity","Value":"sha256-lOTcZxqVz8iqrI9BFx\u002BQBG4pK4KRYH0gFUcExOiM4UE="},{"Name":"label","Value":"_content/TaskDotNet/lib/tinymce/js/tinymce/tinymce.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"415254"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022lOTcZxqVz8iqrI9BFx\u002BQBG4pK4KRYH0gFUcExOiM4UE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/TaskDotNet.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\TaskDotNet.za1yuy05ie.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GyGE2nXm6Pk2GkV\u002BVkYdV1LNQg24qoOovxuzLSHz6O8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1127"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GyGE2nXm6Pk2GkV\u002BVkYdV1LNQg24qoOovxuzLSHz6O8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 08:43:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/TaskDotNet.za1yuy05ie.bundle.scp.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\TaskDotNet.za1yuy05ie.bundle.scp.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"za1yuy05ie"},{"Name":"integrity","Value":"sha256-GyGE2nXm6Pk2GkV\u002BVkYdV1LNQg24qoOovxuzLSHz6O8="},{"Name":"label","Value":"_content/TaskDotNet/TaskDotNet.bundle.scp.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1127"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022GyGE2nXm6Pk2GkV\u002BVkYdV1LNQg24qoOovxuzLSHz6O8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 08:43:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ActivateEmail.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ActivateEmail.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-so98aLKqKnptmAhoPbgYBbmi\u002BM9SFzNaRC9dKgaUOS4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1657"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022so98aLKqKnptmAhoPbgYBbmi\u002BM9SFzNaRC9dKgaUOS4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ActivateEmail.udop0am2c7.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ActivateEmail.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"udop0am2c7"},{"Name":"integrity","Value":"sha256-so98aLKqKnptmAhoPbgYBbmi\u002BM9SFzNaRC9dKgaUOS4="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ActivateEmail.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1657"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022so98aLKqKnptmAhoPbgYBbmi\u002BM9SFzNaRC9dKgaUOS4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/AdminMessgAfterRegister.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\AdminMessgAfterRegister.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-mR2QAn\u002B33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2895"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022mR2QAn\u002B33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/AdminMessgAfterRegister.lhamn20ixv.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\AdminMessgAfterRegister.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lhamn20ixv"},{"Name":"integrity","Value":"sha256-mR2QAn\u002B33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA="},{"Name":"label","Value":"_content/TaskDotNet/Templates/AdminMessgAfterRegister.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2895"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022mR2QAn\u002B33Qwvt2MyO0eIQ4PfCbU8bPjhJuDdBaRGhQA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/AGB.j4m08jd11l.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\AGB.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"j4m08jd11l"},{"Name":"integrity","Value":"sha256-A7Tumj1R\u002BJdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4="},{"Name":"label","Value":"_content/TaskDotNet/Templates/AGB.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"87467"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022A7Tumj1R\u002BJdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/AGB.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\AGB.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-A7Tumj1R\u002BJdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"87467"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022A7Tumj1R\u002BJdtOAwm84k/hod9Wvpf8y7WwuEAb7Js1F4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3467"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-de.jtsyozonns.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jtsyozonns"},{"Name":"integrity","Value":"sha256-Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0="},{"Name":"label","Value":"_content/TaskDotNet/Templates/OTP-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3467"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022Z4rM5p8OQI96kv4iVWP4IEWhvPnaFCFC7QgupkJJdu0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ly6d2QJbkWDhZc7PKFNcL1\u002BtcMBEfTJUYJryuSgOy/0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3439"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ly6d2QJbkWDhZc7PKFNcL1\u002BtcMBEfTJUYJryuSgOy/0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-en.t3if8ouq9v.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"t3if8ouq9v"},{"Name":"integrity","Value":"sha256-ly6d2QJbkWDhZc7PKFNcL1\u002BtcMBEfTJUYJryuSgOy/0="},{"Name":"label","Value":"_content/TaskDotNet/Templates/OTP-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3439"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ly6d2QJbkWDhZc7PKFNcL1\u002BtcMBEfTJUYJryuSgOy/0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-fr.auiifrug0u.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"auiifrug0u"},{"Name":"integrity","Value":"sha256-HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk="},{"Name":"label","Value":"_content/TaskDotNet/Templates/OTP-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3458"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3458"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022HjWT89RwQPSnMCsO7Pig99y/e9h9nRhiWJfYjj6zjKk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-C\u002BbxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3452"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022C\u002BbxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/OTP-it.teadc6g5tj.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\OTP-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"teadc6g5tj"},{"Name":"integrity","Value":"sha256-C\u002BbxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4="},{"Name":"label","Value":"_content/TaskDotNet/Templates/OTP-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3452"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022C\u002BbxxAo9GFO1dtSn6NHFqJjqFs7oFLhyEllsWiZ5/u4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/PaintingEmail.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\PaintingEmail.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9448"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/PaintingEmail.msrqokysyy.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\PaintingEmail.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"msrqokysyy"},{"Name":"integrity","Value":"sha256-yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs="},{"Name":"label","Value":"_content/TaskDotNet/Templates/PaintingEmail.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9448"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022yuhnMbRxIWeYNqDsnJr2e8ygZHEgbF2xjlg8p1FHbcs=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-de.eah06epqyh.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"eah06epqyh"},{"Name":"integrity","Value":"sha256-etp/gX6noPqzc\u002BtXcwmClbQo9WzkFQogW64nBjLc\u002Bog="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendCleaning-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8931"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022etp/gX6noPqzc\u002BtXcwmClbQo9WzkFQogW64nBjLc\u002Bog=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-etp/gX6noPqzc\u002BtXcwmClbQo9WzkFQogW64nBjLc\u002Bog="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8931"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022etp/gX6noPqzc\u002BtXcwmClbQo9WzkFQogW64nBjLc\u002Bog=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GGhA45b9Wn353uT5bAA05Sm/u1Dcovn\u002BYlQaDcRauMg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8689"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022GGhA45b9Wn353uT5bAA05Sm/u1Dcovn\u002BYlQaDcRauMg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-en.w2sz3b30j5.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"w2sz3b30j5"},{"Name":"integrity","Value":"sha256-GGhA45b9Wn353uT5bAA05Sm/u1Dcovn\u002BYlQaDcRauMg="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendCleaning-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8689"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022GGhA45b9Wn353uT5bAA05Sm/u1Dcovn\u002BYlQaDcRauMg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-kqTHJJvLKhtwNcnDMLIqesff4XZnJTu8jrtqj2VcQso="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8765"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022kqTHJJvLKhtwNcnDMLIqesff4XZnJTu8jrtqj2VcQso=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-fr.mva6cvwvi2.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mva6cvwvi2"},{"Name":"integrity","Value":"sha256-kqTHJJvLKhtwNcnDMLIqesff4XZnJTu8jrtqj2VcQso="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendCleaning-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8765"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022kqTHJJvLKhtwNcnDMLIqesff4XZnJTu8jrtqj2VcQso=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-it.3h3v2ga1ew.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3h3v2ga1ew"},{"Name":"integrity","Value":"sha256-V4VWxbz8LvRO7gzbBP8HNWV7Msx4V910pSIFOA9kKCw="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendCleaning-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8890"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022V4VWxbz8LvRO7gzbBP8HNWV7Msx4V910pSIFOA9kKCw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendCleaning-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendCleaning-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-V4VWxbz8LvRO7gzbBP8HNWV7Msx4V910pSIFOA9kKCw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8890"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022V4VWxbz8LvRO7gzbBP8HNWV7Msx4V910pSIFOA9kKCw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Pb50mGLUb8Tk8naZT8cSPET\u002ByQFZt5Pb5y1f1BAvcc8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9098"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022Pb50mGLUb8Tk8naZT8cSPET\u002ByQFZt5Pb5y1f1BAvcc8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-de.njoyhsr5bf.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"njoyhsr5bf"},{"Name":"integrity","Value":"sha256-Pb50mGLUb8Tk8naZT8cSPET\u002ByQFZt5Pb5y1f1BAvcc8="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMoving-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9098"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022Pb50mGLUb8Tk8naZT8cSPET\u002ByQFZt5Pb5y1f1BAvcc8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-en.67i89gh55p.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"67i89gh55p"},{"Name":"integrity","Value":"sha256-IkfHI8K/23JNbFtU0/ZeA5lljcdcjDGxEvDMtZ/DolA="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMoving-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8742"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022IkfHI8K/23JNbFtU0/ZeA5lljcdcjDGxEvDMtZ/DolA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IkfHI8K/23JNbFtU0/ZeA5lljcdcjDGxEvDMtZ/DolA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8742"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022IkfHI8K/23JNbFtU0/ZeA5lljcdcjDGxEvDMtZ/DolA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-UtynHP\u002BHvOhcaDW7AUOk9FR65BfQx\u002BTIz\u002BrCLbRTocI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9143"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022UtynHP\u002BHvOhcaDW7AUOk9FR65BfQx\u002BTIz\u002BrCLbRTocI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-fr.yckhrsfizq.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"yckhrsfizq"},{"Name":"integrity","Value":"sha256-UtynHP\u002BHvOhcaDW7AUOk9FR65BfQx\u002BTIz\u002BrCLbRTocI="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMoving-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9143"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022UtynHP\u002BHvOhcaDW7AUOk9FR65BfQx\u002BTIz\u002BrCLbRTocI=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-it.10yhd1e1lz.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"10yhd1e1lz"},{"Name":"integrity","Value":"sha256-n0g5f6GOMY7bpxTxMFz/N87utPTUwJpTJT\u002BtyTyVyMg="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMoving-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9039"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022n0g5f6GOMY7bpxTxMFz/N87utPTUwJpTJT\u002BtyTyVyMg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMoving-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMoving-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-n0g5f6GOMY7bpxTxMFz/N87utPTUwJpTJT\u002BtyTyVyMg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9039"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022n0g5f6GOMY7bpxTxMFz/N87utPTUwJpTJT\u002BtyTyVyMg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-de.35202tt4v3.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"35202tt4v3"},{"Name":"integrity","Value":"sha256-KU8PDAdNeVn30JRyULL0u804oJzYrc8d6CX3fbuIO0w="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10443"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022KU8PDAdNeVn30JRyULL0u804oJzYrc8d6CX3fbuIO0w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-KU8PDAdNeVn30JRyULL0u804oJzYrc8d6CX3fbuIO0w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10443"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022KU8PDAdNeVn30JRyULL0u804oJzYrc8d6CX3fbuIO0w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-en.26crejesht.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"26crejesht"},{"Name":"integrity","Value":"sha256-pq8UT9f\u002B1W7wPCim8rg/eq1vJDoJOXMrY/zbzBf5M\u002BU="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10120"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022pq8UT9f\u002B1W7wPCim8rg/eq1vJDoJOXMrY/zbzBf5M\u002BU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-pq8UT9f\u002B1W7wPCim8rg/eq1vJDoJOXMrY/zbzBf5M\u002BU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10120"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022pq8UT9f\u002B1W7wPCim8rg/eq1vJDoJOXMrY/zbzBf5M\u002BU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2Ag4451CK9D/tbHSrN82Phm/VfON1oXA1xyJ3u4YNHM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10196"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00222Ag4451CK9D/tbHSrN82Phm/VfON1oXA1xyJ3u4YNHM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-fr.s7peg89fho.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"s7peg89fho"},{"Name":"integrity","Value":"sha256-2Ag4451CK9D/tbHSrN82Phm/VfON1oXA1xyJ3u4YNHM="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10196"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00222Ag4451CK9D/tbHSrN82Phm/VfON1oXA1xyJ3u4YNHM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-78XyFrEg0CvgddjcZqtdQlQIME3qnvntyB4tSl3SJfU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10154"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002278XyFrEg0CvgddjcZqtdQlQIME3qnvntyB4tSl3SJfU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-it.ljlwlkst8z.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendMovingAndCleaning-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ljlwlkst8z"},{"Name":"integrity","Value":"sha256-78XyFrEg0CvgddjcZqtdQlQIME3qnvntyB4tSl3SJfU="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendMovingAndCleaning-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10154"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002278XyFrEg0CvgddjcZqtdQlQIME3qnvntyB4tSl3SJfU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Kgh9Iv4xTTM47IiNy8D1VZLJT0emkWIAsjZ030jlC5k="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6786"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022Kgh9Iv4xTTM47IiNy8D1VZLJT0emkWIAsjZ030jlC5k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-de.iyhlunmbgw.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iyhlunmbgw"},{"Name":"integrity","Value":"sha256-Kgh9Iv4xTTM47IiNy8D1VZLJT0emkWIAsjZ030jlC5k="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendPainting-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6786"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022Kgh9Iv4xTTM47IiNy8D1VZLJT0emkWIAsjZ030jlC5k=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-H1bDI9iREwhfOoF0h8\u002BLZhg9HSJIQCtYmGEk9E9TkiA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6158"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022H1bDI9iREwhfOoF0h8\u002BLZhg9HSJIQCtYmGEk9E9TkiA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-en.n2c8rnzm07.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"n2c8rnzm07"},{"Name":"integrity","Value":"sha256-H1bDI9iREwhfOoF0h8\u002BLZhg9HSJIQCtYmGEk9E9TkiA="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendPainting-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6158"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022H1bDI9iREwhfOoF0h8\u002BLZhg9HSJIQCtYmGEk9E9TkiA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HlH3oW9fXkOMatdvSVtn8Uiirp3296X2lgp8gNHjrYc="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6213"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022HlH3oW9fXkOMatdvSVtn8Uiirp3296X2lgp8gNHjrYc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-fr.mrgowhsaqg.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mrgowhsaqg"},{"Name":"integrity","Value":"sha256-HlH3oW9fXkOMatdvSVtn8Uiirp3296X2lgp8gNHjrYc="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendPainting-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6213"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022HlH3oW9fXkOMatdvSVtn8Uiirp3296X2lgp8gNHjrYc=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-it.8c6w9fv933.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8c6w9fv933"},{"Name":"integrity","Value":"sha256-GF3P5bzii5UZd1JXYVnblhIH3ocNjFUJcBDZ2gjOPOQ="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendPainting-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6740"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022GF3P5bzii5UZd1JXYVnblhIH3ocNjFUJcBDZ2gjOPOQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendPainting-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendPainting-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GF3P5bzii5UZd1JXYVnblhIH3ocNjFUJcBDZ2gjOPOQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6740"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022GF3P5bzii5UZd1JXYVnblhIH3ocNjFUJcBDZ2gjOPOQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ysj1WGDv/hIUOuaSFa/z8GE\u002BKEnCNbmQHqD8nUUqrWw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5613"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ysj1WGDv/hIUOuaSFa/z8GE\u002BKEnCNbmQHqD8nUUqrWw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-de.iusv1d7r96.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"iusv1d7r96"},{"Name":"integrity","Value":"sha256-ysj1WGDv/hIUOuaSFa/z8GE\u002BKEnCNbmQHqD8nUUqrWw="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendWorkers-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5613"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ysj1WGDv/hIUOuaSFa/z8GE\u002BKEnCNbmQHqD8nUUqrWw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-en.d86ygqvryp.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d86ygqvryp"},{"Name":"integrity","Value":"sha256-DbzgzHCzE\u002BJac0wrenLo3v7kLoabIaYgS8evpd/LzQM="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendWorkers-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5104"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022DbzgzHCzE\u002BJac0wrenLo3v7kLoabIaYgS8evpd/LzQM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-DbzgzHCzE\u002BJac0wrenLo3v7kLoabIaYgS8evpd/LzQM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5104"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022DbzgzHCzE\u002BJac0wrenLo3v7kLoabIaYgS8evpd/LzQM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-fr.cpyp6zagkg.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cpyp6zagkg"},{"Name":"integrity","Value":"sha256-ELKQvWRdLNCRMJ77fA7YRLcwZr0SKUzQaCwp3wFJafk="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendWorkers-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5143"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ELKQvWRdLNCRMJ77fA7YRLcwZr0SKUzQaCwp3wFJafk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ELKQvWRdLNCRMJ77fA7YRLcwZr0SKUzQaCwp3wFJafk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5143"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ELKQvWRdLNCRMJ77fA7YRLcwZr0SKUzQaCwp3wFJafk=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NYQkDltgYgoh1Msy\u002BaUHO5L\u002BHY0sG0iCR/9cI17rHCA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5103"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022NYQkDltgYgoh1Msy\u002BaUHO5L\u002BHY0sG0iCR/9cI17rHCA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/RequestFrontendWorkers-it.ti1ay39aoi.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\RequestFrontendWorkers-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ti1ay39aoi"},{"Name":"integrity","Value":"sha256-NYQkDltgYgoh1Msy\u002BaUHO5L\u002BHY0sG0iCR/9cI17rHCA="},{"Name":"label","Value":"_content/TaskDotNet/Templates/RequestFrontendWorkers-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5103"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022NYQkDltgYgoh1Msy\u002BaUHO5L\u002BHY0sG0iCR/9cI17rHCA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-de.hdbkv46alh.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hdbkv46alh"},{"Name":"integrity","Value":"sha256-lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW\u002BeaLXA="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ResetPassword-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2596"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW\u002BeaLXA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW\u002BeaLXA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2596"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022lWV1M7p4FWgs/XrLXNTvJuq24AXAzziRtEluW\u002BeaLXA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-en.bxwiwpvlxj.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bxwiwpvlxj"},{"Name":"integrity","Value":"sha256-L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ResetPassword-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2514"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2514"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022L8vWwB7n8YFoS4m4nAftVPSqNx3AStDi1iMOo83/ZQY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2612"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-fr.pwodc3864l.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"pwodc3864l"},{"Name":"integrity","Value":"sha256-p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ResetPassword-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2612"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022p8js5BUx7dC11HnExmgcxKeVBpWqAr/vTICkFAQDghQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-it.601gadu4t9.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"601gadu4t9"},{"Name":"integrity","Value":"sha256-6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ResetPassword-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2565"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00226hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ResetPassword-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ResetPassword-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2565"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00226hgckUQy1p2zE1IY1/yQVBPHoacOCNvbv5f331HZXWg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl\u002BXD4bA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2337"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl\u002BXD4bA=\u0022"},{"Name":"Last-Modified","Value":"Tue, 29 Jul 2025 15:16:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-de.o58e1cibh7.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"o58e1cibh7"},{"Name":"integrity","Value":"sha256-zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl\u002BXD4bA="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYou-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2337"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022zFFsAZY/Gf0Ac3TtrLt6u/s2OBVjTXdxjKGYl\u002BXD4bA=\u0022"},{"Name":"Last-Modified","Value":"Tue, 29 Jul 2025 15:16:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-en.hemmnn75si.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hemmnn75si"},{"Name":"integrity","Value":"sha256-FwIJV5Ps3z2Kncz7jEnzjuvo\u002B65nd\u002B5g7k2/9BvgeaM="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYou-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2303"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022FwIJV5Ps3z2Kncz7jEnzjuvo\u002B65nd\u002B5g7k2/9BvgeaM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-FwIJV5Ps3z2Kncz7jEnzjuvo\u002B65nd\u002B5g7k2/9BvgeaM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2303"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022FwIJV5Ps3z2Kncz7jEnzjuvo\u002B65nd\u002B5g7k2/9BvgeaM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-fr.6nh9u6qvzv.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"6nh9u6qvzv"},{"Name":"integrity","Value":"sha256-biio33qLgD\u002BOz3busPRlCyDjih1WDXjo\u002B9caC57oM4o="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYou-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2373"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022biio33qLgD\u002BOz3busPRlCyDjih1WDXjo\u002B9caC57oM4o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-biio33qLgD\u002BOz3busPRlCyDjih1WDXjo\u002B9caC57oM4o="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2373"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022biio33qLgD\u002BOz3busPRlCyDjih1WDXjo\u002B9caC57oM4o=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-it.9q9oxzsxlo.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9q9oxzsxlo"},{"Name":"integrity","Value":"sha256-v4uLFordz1mainzhWH7i84VDaMaZ\u002ByDtJnk8bHC60jY="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYou-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2381"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022v4uLFordz1mainzhWH7i84VDaMaZ\u002ByDtJnk8bHC60jY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYou-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYou-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-v4uLFordz1mainzhWH7i84VDaMaZ\u002ByDtJnk8bHC60jY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2381"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022v4uLFordz1mainzhWH7i84VDaMaZ\u002ByDtJnk8bHC60jY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-de.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr\u002B4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2605"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr\u002B4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 29 Jul 2025 15:16:06 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-de.nt4xq5ndu1.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-de.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nt4xq5ndu1"},{"Name":"integrity","Value":"sha256-q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr\u002B4="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYouCustomer-de.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2605"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022q9QcxtnT34lmtcNYfCUJZTwcDbFK84YnXxRSgb8hr\u002B4=\u0022"},{"Name":"Last-Modified","Value":"Tue, 29 Jul 2025 15:16:06 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-en.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uTt5oQGoIqqT6Is4ATphkPEdcRIIf\u002BM0Tz\u002B/F4gUUeM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2710"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022uTt5oQGoIqqT6Is4ATphkPEdcRIIf\u002BM0Tz\u002B/F4gUUeM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-en.nssyq9ewpc.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-en.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"nssyq9ewpc"},{"Name":"integrity","Value":"sha256-uTt5oQGoIqqT6Is4ATphkPEdcRIIf\u002BM0Tz\u002B/F4gUUeM="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYouCustomer-en.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2710"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022uTt5oQGoIqqT6Is4ATphkPEdcRIIf\u002BM0Tz\u002B/F4gUUeM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-fr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2822"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-fr.sa0j1gvmik.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-fr.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"sa0j1gvmik"},{"Name":"integrity","Value":"sha256-vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYouCustomer-fr.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2822"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022vCZQCDfLa1eqTTDC1eBxsFlJByq2/8rt559j/R2IKrM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-it.4sfjp0ek4d.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4sfjp0ek4d"},{"Name":"integrity","Value":"sha256-XJA5i0qj7q\u002BsC\u002B9uQYm5i3v0EOFHIeLQx\u002BTxYD\u002BVKiw="},{"Name":"label","Value":"_content/TaskDotNet/Templates/ThankYouCustomer-it.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2749"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022XJA5i0qj7q\u002BsC\u002B9uQYm5i3v0EOFHIeLQx\u002BTxYD\u002BVKiw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/TaskDotNet/Templates/ThankYouCustomer-it.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\Templates\ThankYouCustomer-it.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XJA5i0qj7q\u002BsC\u002B9uQYm5i3v0EOFHIeLQx\u002BTxYD\u002BVKiw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2749"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022XJA5i0qj7q\u002BsC\u002B9uQYm5i3v0EOFHIeLQx\u002BTxYD\u002BVKiw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 27 Jul 2025 07:47:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>