﻿@model RechargeCreditDto
@{
    ViewData["Title"] = "RechargeCredit";
}


<section style="height:100%">
    <div class="d-flex justify-content-center align-items-center flex-column" style="height:100%">
        <div class="row g-0">
            <div class="col-md-6 d-flex align-items-center">
                <div class="card-body text-black" style="padding:3.5rem">
                    <h4 class="mb-12 text-white">
                        @SharedLocalizer["RechargeCreditHead1"]
                    </h4>
                    <h5 class="text-white mb-12">
                        @SharedLocalizer["RechargeCreditHead2"]
                        <a style="text-decoration: underline; color:#40FFFF; font-size: 16px" asp-action="TermsAndConditions" target="_blank">
                            @SharedLocalizer["Privacy Policy"]      <!-- -->
                        </a>
                    </h5>
                    <!--
                    <p class="text-white fs-5">
                        @SharedLocalizer["RechargeCreditHead2"]
                        <a style="text-decoration: underline; color:#40FFFF" asp-action="TermsAndConditions" target="_blank">
                            @SharedLocalizer["Privacy Policy"]
                        </a>
                    </p>
                    -->

                    <div class="mt-12">
                        <div class="d-flex flex-wrap gap-3 fs-5 justify-content-between">
                            <a style="text-decoration: underline; color: #99CC33; font-weight: bold;" asp-action="TermsAndConditions" target="_blank">
                                @SharedLocalizer["TermsAndConditions"]
                            </a>
                            <a style="text-decoration: underline; color: #99CC33; font-weight: bold;" asp-action="PrivacyPolicy" target="_blank">
                                @SharedLocalizer["PrivacyPolicy"]
                            </a>
                            <a style="text-decoration: underline; color: #99CC33; font-weight: bold;" asp-action="Disclaimer" target="_blank">
                                @SharedLocalizer["Disclaimer"]
                            </a>
                            <a style="text-decoration: underline; color: #99CC33; font-weight: bold;" asp-action="LegalNotice" target="_blank">
                                @SharedLocalizer["LegalNotice"]
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 d-flex justify-content-center align-items-center flex-column">
                <div class="row g-0 p-10 justify-content-center align-items-center align-content-center" style="width:80%; background-color: white;">
                    <img class="logo-img" src="~/Dashboard/assets/img/logo.png" style="width:300px">
                    <h2 class="text-center mt-10" style="color:#99CC33;">
                        @SharedLocalizer["Recharge credit"]
                    </h2>

                    <form id="RechargeCreditForm" asp-action="RechargeCredit" method="post">
                        <div class="row justify-content-center">
                            <div class="mb-3 col-md-12">
                                <label for="CurrentBalance" class="form-label fs-5">
                                    @SharedLocalizer["Current Balance"]
                                </label>
                                <input asp-for="CurrentBalance" class="form-control" readonly />
                            </div>

                            <div class="mb-3 col-md-12">
                                <label for="Amount" class="form-label fs-5">
                                    @SharedLocalizer["AddAmount"]
                                </label>
                                <input class="form-control"
                                       type="number"
                                       placeholder="0.00"
                                       name="Amount"
                                       id="Amount"
                                       step="0.01"
                                       min="0.00"
                                       value="@ViewBag.Amount"
                                       oninput="validateAmount(this)" />
                            </div>

                            <div class="d-flex mb-3 col-md-12 gap-2">
                                <input class="form-check-input" type="checkbox" value="" id="defaultCheck1" required>
                                <label class="form-check-label fs-5 text-danger" for="defaultCheck1">@SharedLocalizer["ReadTermsAndCondotions"]</label>
                            </div>
                        </div>

                        <div class="mt-3 text-center">
                            <a asp-controller="Home" asp-action="Index" class="btn btn-lg btn-outline-secondary px-11">@SharedLocalizer["Cancel"]</a>
                            <input type="button" class="btn btn-lg btn-primary px-11 ms-3" value="@SharedLocalizer["Checkout"]" data-bs-toggle="modal" data-bs-target="#checkoutModal" id="checkoutBtn" disabled />
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</section>



<!-- Modal -->
<div class="modal fade" id="checkoutModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header justify-content-center">
                <h3 class="modal-title" id="exampleModalLabel">@SharedLocalizer["Recharge credit"]</h3>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div>
                    <p>
                        @SharedLocalizer["CheckoutRedirectMessage"]
                    </p>
                </div>
                <div class="row justify-content-center mt-10" style="gap:20px">
                    <button class="btn btn-lg btn-danger col-4" type="button" data-bs-dismiss="modal" aria-label="Close">@SharedLocalizer["Cancel"]</button>
                    <button id="ConfiremCheckoutBtn" class="btn btn-lg btn-primary col-4">@SharedLocalizer["Ok"]</button>
                </div>
            </div>
        </div>
    </div>
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Function to validate the amount
        function validateAmount(input) {
            let value = parseFloat(input.value);
            if (isNaN(value) || value < 0.01) {
                input.value = ""; // Clear invalid input
                return false;
            }
            return true;
        }

        // Enable/disable Checkout button based on checkbox
        document.getElementById("defaultCheck1").addEventListener("change", function () {
            document.getElementById("checkoutBtn").disabled = !this.checked;
        });

        // Handle Checkout Button Click
        document.getElementById("ConfiremCheckoutBtn").addEventListener("click", function () {
            const amountInput = document.getElementById("Amount");
            const termsCheckbox = document.getElementById("defaultCheck1");

            // Validate the amount
            if (!validateAmount(amountInput)) {
                Swal.fire({
                    icon: "error",
                    title: "@Html.Raw(SharedLocalizer["InvalidAmount"])",
                    text: "@Html.Raw(SharedLocalizer["AmountMustBeGreaterThanZero"])"
                });
                $('#checkoutModal').modal('hide');
                return;
            }

            // Validate terms checkbox
            if (!termsCheckbox.checked) {
                Swal.fire({
                    icon: "error",
                    title: "@Html.Raw(SharedLocalizer["TermsNotAccepted"])",
                    text: "@Html.Raw(SharedLocalizer["PleaseAcceptTerms"])"
                });
                $('#checkoutModal').modal('hide');
                return;
            }

            $('#loader').show();
            // Submit the form programmatically
            document.getElementById("RechargeCreditForm").submit();
        });
    </script>
}