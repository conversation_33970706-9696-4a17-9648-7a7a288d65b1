{"Files": [{"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\TaskDotNet.bundle.scp.css", "PackagePath": "staticwebassets\\TaskDotNet.za1yuy05ie.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\css\\demo.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\css\\demo.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\AGB.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\AGB.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\ActivateEmail.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\ActivateEmail.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Attention-removebg-preview.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Attention-removebg-preview.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard1.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard2.jpeg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard2.jpeg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard_01.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard_01.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard_02.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard_02.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard_03.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard_03.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Foto_Register_Form.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Foto_Register_Form.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Gekauft.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Gekauft.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\LOGO.svg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\LOGO.svg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Login.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Login.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Login2.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Login2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Nathing.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Nathing.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Partner.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Partner.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\PostFinance.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\PostFinance.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Post_Code.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Post_Code.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Saldo.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Saldo.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Schloss.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Schloss.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Stop.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Stop.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Twint.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Twint.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Visa_Konto.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Visa_Konto.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\1.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\5.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\5.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\6.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\6.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\7.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\7.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\backgrounds\\18.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\backgrounds\\18.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\cleaning.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\cleaning.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\color.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\color.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\de-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\de-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\1.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\1.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\11.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\11.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\12.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\12.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\13.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\13.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\17.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\17.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\18.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\18.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\19.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\19.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\2.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\2.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\20.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\20.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\3.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\3.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\4.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\4.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\5.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\5.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\7.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\7.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\favicon\\favicon.ico", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\favicon\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\fr-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\fr-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\gisper.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\gisper.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconfacebook.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\iconfacebook.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconinsta.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\iconinsta.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconlinked.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\iconlinked.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\asana.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\asana.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\behance.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\behance.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\dribbble.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\dribbble.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\facebook.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\facebook.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\github.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\github.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\google.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\google.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\instagram.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\instagram.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\mailchimp.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\mailchimp.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\slack.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\slack.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\twitter.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\twitter.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-primary.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\cc-primary.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-success.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\cc-success.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-warning.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\cc-warning.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart-success.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\chart-success.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\chart.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\paypal.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\paypal.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet-info.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\wallet-info.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\wallet.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icontwitter.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icontwitter.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-doing-yoga-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\girl-doing-yoga-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-with-laptop-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\girl-with-laptop-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\in-prograss.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\in-prograss.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\man-with-laptop-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\man-with-laptop-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light2.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\it-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\it-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\language.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\language.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\lock.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\lock.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\logo.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\logo.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\moving and cleaning.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\moving and cleaning.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\moving.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\moving.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\painting.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\painting.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\pizaa.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\pizaa.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\thank-you.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\thank-you.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\thankyou.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\thankyou.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\us-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\us-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\config.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\config.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\main.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\main.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\core.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\core.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\page-auth.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\page-auth.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\theme-default.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\theme-default.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.eot", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.eot"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.svg", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.svg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.ttf", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.ttf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff2", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff2"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\jquery\\jquery.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\jquery\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\bootstrap.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\helpers.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\helpers.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\menu.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\menu.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\template-customizer.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\template-customizer.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\perfect-scrollbar\\perfect-scrollbar.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\popper\\popper.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\popper\\popper.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\AGB.pdf", "PackagePath": "staticwebassets\\Templates\\AGB.pdf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ActivateEmail.html", "PackagePath": "staticwebassets\\Templates\\ActivateEmail.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\AdminMessgAfterRegister.html", "PackagePath": "staticwebassets\\Templates\\AdminMessgAfterRegister.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-de.html", "PackagePath": "staticwebassets\\Templates\\OTP-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-en.html", "PackagePath": "staticwebassets\\Templates\\OTP-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-fr.html", "PackagePath": "staticwebassets\\Templates\\OTP-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\OTP-it.html", "PackagePath": "staticwebassets\\Templates\\OTP-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\PaintingEmail.html", "PackagePath": "staticwebassets\\Templates\\PaintingEmail.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-de.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendCleaning-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-en.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendCleaning-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-fr.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendCleaning-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendCleaning-it.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendCleaning-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-de.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMoving-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-en.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMoving-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-fr.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMoving-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMoving-it.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMoving-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-de.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMovingAndCleaning-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-en.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMovingAndCleaning-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-fr.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMovingAndCleaning-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendMovingAndCleaning-it.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendMovingAndCleaning-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-de.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendPainting-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-en.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendPainting-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-fr.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendPainting-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendPainting-it.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendPainting-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-de.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendWorkers-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-en.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendWorkers-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-fr.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendWorkers-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\RequestFrontendWorkers-it.html", "PackagePath": "staticwebassets\\Templates\\RequestFrontendWorkers-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-de.html", "PackagePath": "staticwebassets\\Templates\\ResetPassword-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-en.html", "PackagePath": "staticwebassets\\Templates\\ResetPassword-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-fr.html", "PackagePath": "staticwebassets\\Templates\\ResetPassword-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ResetPassword-it.html", "PackagePath": "staticwebassets\\Templates\\ResetPassword-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-de.html", "PackagePath": "staticwebassets\\Templates\\ThankYou-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-en.html", "PackagePath": "staticwebassets\\Templates\\ThankYou-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-fr.html", "PackagePath": "staticwebassets\\Templates\\ThankYou-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYou-it.html", "PackagePath": "staticwebassets\\Templates\\ThankYou-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-de.html", "PackagePath": "staticwebassets\\Templates\\ThankYouCustomer-de.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-en.html", "PackagePath": "staticwebassets\\Templates\\ThankYouCustomer-en.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-fr.html", "PackagePath": "staticwebassets\\Templates\\ThankYouCustomer-fr.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\Templates\\ThankYouCustomer-it.html", "PackagePath": "staticwebassets\\Templates\\ThankYouCustomer-it.html"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\flatpickr\\flatpickr.min.css", "PackagePath": "staticwebassets\\lib\\flatpickr\\flatpickr.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\flatpickr\\flatpickr.min.js", "PackagePath": "staticwebassets\\lib\\flatpickr\\flatpickr.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-ui\\jquery-ui.css", "PackagePath": "staticwebassets\\lib\\jquery-ui\\jquery-ui.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-ui\\jquery-ui.js", "PackagePath": "staticwebassets\\lib\\jquery-ui\\jquery-ui.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\sweetalert2\\sweet-alerts.init.js", "PackagePath": "staticwebassets\\lib\\sweetalert2\\sweet-alerts.init.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\sweetalert2\\sweetalert2.min.css", "PackagePath": "staticwebassets\\lib\\sweetalert2\\sweetalert2.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\sweetalert2\\sweetalert2.min.js", "PackagePath": "staticwebassets\\lib\\sweetalert2\\sweetalert2.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\icons\\default\\icons.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\icons\\default\\icons.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\de.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\langs\\de.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\fr.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\langs\\fr.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\langs\\it.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\langs\\it.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\license.txt", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\license.txt"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\models\\dom\\model.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\models\\dom\\model.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\advlist\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\advlist\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\anchor\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\anchor\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autolink\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\autolink\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autoresize\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\autoresize\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\autosave\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\autosave\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\charmap\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\charmap\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\code\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\code\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\codesample\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\codesample\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\directionality\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\directionality\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojiimages.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\js\\emojis.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\emoticons\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\fullscreen\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\fullscreen\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\help\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\help\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\image\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\image\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\importcss\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\importcss\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\insertdatetime\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\insertdatetime\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\link\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\link\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\lists\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\lists\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\media\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\media\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\nonbreaking\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\nonbreaking\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\pagebreak\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\pagebreak\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\preview\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\preview\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\quickbars\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\quickbars\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\save\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\save\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\searchreplace\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\searchreplace\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\table\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\table\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\template\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\template\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\visualblocks\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\visualblocks\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\visualchars\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\visualchars\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\plugins\\wordcount\\plugin.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\plugins\\wordcount\\plugin.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\dark\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\content\\dark\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\default\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\content\\default\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\document\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\content\\document\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5-dark\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5-dark\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\content\\tinymce-5\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\content\\writer\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\content\\writer\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.inline.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.inline.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.shadowdom.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide-dark\\skin.shadowdom.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.inline.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.inline.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.shadowdom.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\oxide\\skin.shadowdom.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.inline.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.inline.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.shadowdom.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5-dark\\skin.shadowdom.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.inline.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.inline.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\content.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.shadowdom.min.css", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\skins\\ui\\tinymce-5\\skin.shadowdom.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\themes\\silver\\theme.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\themes\\silver\\theme.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\tinymce.d.ts", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\tinymce.d.ts"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_27_07_2025\\TaskDotNet\\wwwroot\\lib\\tinymce\\js\\tinymce\\tinymce.min.js", "PackagePath": "staticwebassets\\lib\\tinymce\\js\\tinymce\\tinymce.min.js"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.TaskDotNet.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.TaskDotNet.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.TaskDotNet.props", "PackagePath": "build\\TaskDotNet.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.TaskDotNet.props", "PackagePath": "buildMultiTargeting\\TaskDotNet.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.TaskDotNet.props", "PackagePath": "buildTransitive\\TaskDotNet.props"}], "ElementsToRemove": []}