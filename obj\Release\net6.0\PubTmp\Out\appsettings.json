{
  "ConnectionStrings": {
    //"DefultConnection": "server=.\\SQLEXPRESS;database=TaskNet;Integrated Security=true;"
    "DefultConnection": "Data Source=SQL6032.site4now.net;Initial Catalog=db_ab7ebc_taskdotnet25;User Id=db_ab7ebc_taskdotnet25_admin;Password=************"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },

  //  "MailSettings": {
  //    "CompanyMail": "<EMAIL>",
  //    "Mail": "<EMAIL>",
  //    "DisplayName": "TaskDotNet",
  //    "Password": "pqycpmacasdrelsd",
  //    "Host": "smtp.gmail.com",
  //    "Port": 587
  //  },

  "MailSettings": {
    "CompanyMail": "<EMAIL>",
    "Mail": "<EMAIL>",
    "DisplayName": "TaskDotNer",
    "Password": "Koko2025@MAFM@",
    "Host": "mail5019.site4now.net",
    "Port": 587
  },

  "EmailImages": {
    "companyLogo": "https://res.cloudinary.com/ddk7dvdjv/image/upload/v1747589470/Logo_1_ykdloy.png"
  },

  "ProjectsLinks": {
    "ActivitesLink": "https://partner.wedo24.ch/Partner/Activities"
  },

  "AllowedHosts": "*",


  "PostFinancePayment": {
    "SpaceId": 62437,
    "ApplicationUserID": "107021",
    "AuthenticationKey": "wY2ROuDUMIIILBx+Rb3EN8xM8SY5pBvfaBXIqvccoe4="
  }

}
